import path from "path";
import fs from "fs/promises";
import mime from "mime-types";
import { createHash } from "crypto";

class Cache {
  private static readonly cacheDir = ".cache/preload"; // 缓存目录
  private loadedCacheDir: boolean = false; // 是否已加载缓存目录
  private cachedFileMap!: Map<string, string>; // 缓存文件名映射，key 为不带扩展名的文件名，value 为完整的文件名

  async findCache(filenameWithoutExt: string) {
    if (!this.loadedCacheDir) {
      // 加载缓存目录
      await fs.mkdir(Cache.cacheDir, { recursive: true });
      this.cachedFileMap = (await fs.readdir(Cache.cacheDir)).reduce(
        (map, file) => {
          const nameWithoutExt = path.parse(file).name;
          map.set(nameWithoutExt, file);
          return map;
        },
        new Map<string, string>()
      );
      this.loadedCacheDir = true;
    }

    return this.cachedFileMap.get(filenameWithoutExt);
  }

  async download(url: string, outdir: string): Promise<string> {
    await fs.mkdir(outdir, { recursive: true });

    const filenameWithoutExt = createHash("md5").update(url).digest("hex");
    let filename = await this.findCache(filenameWithoutExt);
    if (!filename) {
      const resp = await fetch(url);
      if (!resp.ok)
        throw new Error(
          `Failed to fetch: ${resp.status} ${resp.statusText}\n${url}`
        );
      const buffer = Buffer.from(await resp.arrayBuffer());
      const ext =
        mime.extension(resp.headers.get("content-type") || "") || "jpg";
      filename = `${filenameWithoutExt}.${ext}`;
      await fs.writeFile(path.join(Cache.cacheDir, filename), buffer);
      this.cachedFileMap.set(filenameWithoutExt, filename);

      console.log(`[preload] Downloaded file "${filename}"`);
    } else {
      // console.debug(`[preload] Using cached file "${filename}"`);
    }

    // copy
    await fs.copyFile(
      path.join(Cache.cacheDir, filename),
      path.join(outdir, filename)
    );
    return filename;
  }
}

const cache = new Cache();

export async function preloadImage(
  url: string,
  outdir: string
): Promise<string> {
  return await cache.download(url, outdir);
}
