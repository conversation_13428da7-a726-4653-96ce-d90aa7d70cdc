import { expect, test } from "vitest";
import { preloadImage } from "./preload";

test("preloadImage creates directory", async () => {
  const outdir = "dist/_images/";
  const output = await preloadImage("https://dummyimage.com/300", outdir);
  expect(output).toBe("195cd9fffccc031de6161cb486a49f61.png");

  // Check if the directory was created
  const fs = await import("fs/promises");
  const exists = await fs
    .access(outdir)
    .then(() => true)
    .catch(() => false);

  expect(exists).toBe(true);
});
