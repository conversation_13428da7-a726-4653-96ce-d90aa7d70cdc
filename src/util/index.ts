/**
 *
 * @param param0
 * @param options.encode 是否对参数进行编码。构建静态文件路径时不要编码，生成 URL 时必须编码。
 * @returns
 */
export function buildPagePath(
  {
    sizedFor,
    page,
    color,
    tag,
  }: {
    sizedFor?: string;
    page?: number;
    color?: string;
    tag?: string;
  },
  options: { encode: boolean } = { encode: true }
) {
  const parts: string[] = [];
  if (sizedFor !== undefined) {
    parts.push(sizedFor);
  }
  if (color !== undefined) {
    parts.push("c", options.encode ? encodeURIComponent(color) : color); // color 值来自服务端，所以前缀 c 目录，防止路径冲突
  }
  if (tag !== undefined) {
    parts.push("t", options.encode ? encodeURIComponent(tag) : tag);
  }
  if (page !== undefined) {
    parts.push(page.toString());
  }
  return "/" + parts.join("/");
}
