import iphone13Styles from "./iphone13.module.scss";
import iMacStyles from "./imac.module.scss";
import clsx from "clsx";

interface Props {
  backgroundImage: string;
}

export function Iphone13ProMax({ backgroundImage, ...props }: Props) {
  return (
    <section className={iphone13Styles.container}>
      <div
        className={iphone13Styles["outside-border"]}
        style={{ backgroundImage: `url(${backgroundImage})` }}
      >
        <div className={iphone13Styles["silencer"]}></div>
        <div className={iphone13Styles["volume-up"]}></div>
        <div className={iphone13Styles["volume-down"]}></div>
        <div className={iphone13Styles["button-on"]}></div>
        <div className={iphone13Styles["inside-border"]}>
          <div className={iphone13Styles["camera"]}>
            <div className={iphone13Styles["camera-dot"]}>
              <div className={iphone13Styles["camera-dot-2"]}></div>
              <div className={iphone13Styles["camera-dot-3"]}></div>
            </div>
            <div className={iphone13Styles["camera-speaker"]}></div>
          </div>

          <div className={iphone13Styles["lock"]}>
            <div className={iphone13Styles["lock-locked"]}></div>
          </div>

          <div className={iphone13Styles["time"]}>19:53</div>

          <div className={iphone13Styles["t-r-info"]}>
            <div className={iphone13Styles["dots"]}>...</div>
            <div className={iphone13Styles["battery"]}>
              <div className={iphone13Styles["bar"]}></div>
              <div className={iphone13Styles["dot"]}></div>
            </div>
          </div>

          <div className={iphone13Styles["date"]}>Tuesday, 9 August</div>

          <div className={iphone13Styles["torch-outter"]}>
            <div className={iphone13Styles["light"]}></div>
            <div className={iphone13Styles["top"]}></div>
            <div className={iphone13Styles["switch-top"]}></div>
            <div className={iphone13Styles["switch-section"]}></div>
            <div className={iphone13Styles["switch"]}>
              <div className={iphone13Styles["dot"]}></div>
            </div>
          </div>

          <div className={iphone13Styles["camera-outter"]}>
            <div className={iphone13Styles["box"]}></div>
            <div className={iphone13Styles["eye"]}></div>
            <div className={iphone13Styles["circle"]}></div>
            <div className={iphone13Styles["dot"]}></div>
          </div>

          <div className={iphone13Styles["bottom-line"]}></div>
        </div>
      </div>
    </section>
  );
}

export function IMac({ backgroundImage }: Props) {
  return (
    <div className="flex justify-center scale-50 sm:scale-70 lg:scale-60">
      <div className={clsx(iMacStyles["device"], iMacStyles["device-imac"])}>
        <div className={iMacStyles["device-frame"]}>
          <img
            className={iMacStyles["device-screen"]}
            src={backgroundImage}
            style={{ objectFit: "cover" }}
            loading="lazy"
          />
        </div>
        <div className={iMacStyles["device-stripe"]}></div>
        <div className={iMacStyles["device-header"]}></div>
        <div className={iMacStyles["device-sensors"]}></div>
        <div className={iMacStyles["device-btns"]}></div>
        <div className={iMacStyles["device-power"]}></div>
      </div>
    </div>
  );
}
