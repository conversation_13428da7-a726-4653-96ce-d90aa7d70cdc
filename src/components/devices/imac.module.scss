// iMac 24” (2021)
@use "sass:color";

$device-silver: #edeef0;
$device-silver-dark: #d4d5d7;
$device-blue: #edeef0;
$device-blue-dark: #b4c7da;
$device-green: #edeef0;
$device-green-dark: #bbd0c8;
$device-pink: #edeef0;
$device-pink-dark: #edccc6;
$device-yellow: #edeef0;
$device-yellow-dark: #f4d595;
$device-orange: #edeef0;
$device-orange-dark: #e9b5a0;
$device-purple: #edeef0;
$device-purple-dark: #c4c4e5;
$device-panel: #0d0d0d;

.device-imac {
  height: 540px;
  width: 640px;

  .device-frame {
    background: $device-silver;
    border-radius: 18px;
    box-shadow: inset 0 0 0 1px $device-silver-dark;
    height: 440px;
    padding: 16px 16px 80px 16px;
    position: relative;
    width: 640px;

    &::after {
      background: $device-silver-dark;
      border-radius: 0 0 18px 18px;
      bottom: 1px;
      box-shadow: inset 0 0 18px 0
        color.adjust($device-silver-dark, $lightness: -5%);
      content: "";
      height: 63px;
      left: 1px;
      position: absolute;
      width: 638px;
    }
    &::before {
      background: color.adjust($device-panel, $lightness: -3%);
      border-radius: 50%;
      content: "";
      height: 6px;
      left: 50%;
      margin-left: -3px;
      position: absolute;
      text-align: center;
      top: 6px;
      width: 6px;
      z-index: 9;
    }
  }

  // 4480-by-2520-pixel resolution
  .device-screen {
    border: 2px solid color.adjust($device-panel, $lightness: 2%);
    border-radius: 2px;
    height: 342px;
    width: 608px;
  }

  .device-power {
    &::after {
      display: block;
      background: radial-gradient(
        circle at center,
        $device-silver-dark 85%,
        color.adjust($device-silver-dark, $lightness: -20%) 100%
      );
      border-top: 1px solid $device-silver-dark;
      content: "";
      height: 6px;
      margin: 0 auto;
      position: relative;
      width: 152px;
    }
    &::before {
      display: block;
      background: linear-gradient(
        to bottom,
        color.adjust($device-silver-dark, $lightness: -20%) 0,
        color.adjust($device-silver-dark, $lightness: -5%) 40%,
        color.adjust($device-silver-dark, $lightness: -5%) 85%,
        color.adjust($device-silver, $lightness: 15%) 90%,
        color.adjust($device-silver-dark, $lightness: -40%) 100%
      );
      content: "";
      height: 92px;
      margin: 0 auto;
      position: relative;
      width: 152px;
    }
  }

  .device-home {
    background: transparent;
    border-radius: 0 0 3px 3px;
    bottom: 0;
    box-shadow: -61px 0 $device-silver-dark, 61px 0 $device-silver-dark;
    height: 2px;
    left: 50%;
    margin-left: -15px;
    position: absolute;
    width: 30px;
  }

  // Blue edition
  &.device-blue {
    .device-frame {
      box-shadow: inset 0 0 0 2px $device-blue-dark;

      &::after {
        background: $device-blue-dark;
        box-shadow: inset 0 0 18px 0
          color.adjust($device-blue-dark, $lightness: -5%);
      }
    }

    .device-power {
      &::after {
        background: radial-gradient(
          circle at center,
          $device-blue-dark 85%,
          color.adjust($device-blue-dark, $lightness: -20%) 100%
        );
        border-top-color: $device-blue-dark;
      }
      &::before {
        background: linear-gradient(
          to bottom,
          color.adjust($device-blue-dark, $lightness: -20%) 0,
          color.adjust($device-blue-dark, $lightness: -5%) 40%,
          color.adjust($device-blue-dark, $lightness: -5%) 85%,
          color.adjust($device-blue, $lightness: 15%) 90%,
          color.adjust($device-blue-dark, $lightness: -40%) 100%
        );
      }
    }

    .device-home {
      box-shadow: -61px 0 $device-blue-dark, 61px 0 $device-blue-dark;
    }
  }

  // Green edition
  &.device-green {
    .device-frame {
      box-shadow: inset 0 0 0 2px $device-green-dark;

      &::after {
        background: $device-green-dark;
        box-shadow: inset 0 0 18px 0
          color.adjust($device-green-dark, $lightness: -5%);
      }
    }

    .device-power {
      &::after {
        background: radial-gradient(
          circle at center,
          $device-green-dark 85%,
          color.adjust($device-green-dark, $lightness: -20%) 100%
        );
        border-top-color: $device-green-dark;
      }
      &::before {
        background: linear-gradient(
          to bottom,
          color.adjust($device-green-dark, $lightness: -20%) 0,
          color.adjust($device-green-dark, $lightness: -5%) 40%,
          color.adjust($device-green-dark, $lightness: -5%) 85%,
          color.adjust($device-green, $lightness: 15%) 90%,
          color.adjust($device-green-dark, $lightness: -40%) 100%
        );
      }
    }

    .device-home {
      box-shadow: -61px 0 $device-green-dark, 61px 0 $device-green-dark;
    }
  }

  // Pink edition
  &.device-pink {
    .device-frame {
      box-shadow: inset 0 0 0 2px $device-pink-dark;

      &::after {
        background: $device-pink-dark;
        box-shadow: inset 0 0 18px 0
          color.adjust($device-pink-dark, $lightness: -5%);
      }
    }

    .device-power {
      &::after {
        background: radial-gradient(
          circle at center,
          $device-pink-dark 85%,
          color.adjust($device-pink-dark, $lightness: -20%) 100%
        );
        border-top-color: $device-pink-dark;
      }
      &::before {
        background: linear-gradient(
          to bottom,
          color.adjust($device-pink-dark, $lightness: -20%) 0,
          color.adjust($device-pink-dark, $lightness: -5%) 40%,
          color.adjust($device-pink-dark, $lightness: -5%) 85%,
          color.adjust($device-green, $lightness: 15%) 90%,
          color.adjust($device-pink-dark, $lightness: -40%) 100%
        );
      }
    }

    .device-home {
      box-shadow: -61px 0 $device-pink-dark, 61px 0 $device-pink-dark;
    }
  }

  // Yello edition
  &.device-yellow {
    .device-frame {
      box-shadow: inset 0 0 0 2px $device-yellow-dark;

      &::after {
        background: $device-yellow-dark;
        box-shadow: inset 0 0 18px 0
          color.adjust($device-yellow-dark, $lightness: -5%);
      }
    }

    .device-power {
      &::after {
        background: radial-gradient(
          circle at center,
          $device-yellow-dark 85%,
          color.adjust($device-yellow-dark, $lightness: -20%) 100%
        );
        border-top-color: $device-yellow-dark;
      }
      &::before {
        background: linear-gradient(
          to bottom,
          color.adjust($device-yellow-dark, $lightness: -20%) 0,
          color.adjust($device-yellow-dark, $lightness: -5%) 40%,
          color.adjust($device-yellow-dark, $lightness: -5%) 85%,
          color.adjust($device-green, $lightness: 15%) 90%,
          color.adjust($device-yellow-dark, $lightness: -40%) 100%
        );
      }
    }

    .device-home {
      box-shadow: -61px 0 $device-yellow-dark, 61px 0 $device-yellow-dark;
    }
  }

  // Orange edition
  &.device-orange {
    .device-frame {
      box-shadow: inset 0 0 0 2px $device-orange-dark;

      &::after {
        background: $device-orange-dark;
        box-shadow: inset 0 0 18px 0
          color.adjust($device-orange-dark, $lightness: -5%);
      }
    }

    .device-power {
      &::after {
        background: radial-gradient(
          circle at center,
          $device-orange-dark 85%,
          color.adjust($device-orange-dark, $lightness: -20%) 100%
        );
        border-top-color: $device-orange-dark;
      }
      &::before {
        background: linear-gradient(
          to bottom,
          color.adjust($device-orange-dark, $lightness: -20%) 0,
          color.adjust($device-orange-dark, $lightness: -5%) 40%,
          color.adjust($device-orange-dark, $lightness: -5%) 85%,
          color.adjust($device-green, $lightness: 15%) 90%,
          color.adjust($device-orange-dark, $lightness: -40%) 100%
        );
      }
    }

    .device-home {
      box-shadow: -61px 0 $device-orange-dark, 61px 0 $device-orange-dark;
    }
  }

  // Purple edition
  &.device-purple {
    .device-frame {
      box-shadow: inset 0 0 0 2px $device-purple-dark;

      &::after {
        background: $device-purple-dark;
        box-shadow: inset 0 0 18px 0
          color.adjust($device-purple-dark, $lightness: -5%);
      }
    }

    .device-power {
      &::after {
        background: radial-gradient(
          circle at center,
          $device-purple-dark 85%,
          color.adjust($device-purple-dark, $lightness: -20%) 100%
        );
        border-top-color: $device-purple-dark;
      }
      &::before {
        background: linear-gradient(
          to bottom,
          color.adjust($device-purple-dark, $lightness: -20%) 0,
          color.adjust($device-purple-dark, $lightness: -5%) 40%,
          color.adjust($device-purple-dark, $lightness: -5%) 85%,
          color.adjust($device-green, $lightness: 15%) 90%,
          color.adjust($device-purple-dark, $lightness: -40%) 100%
        );
      }
    }

    .device-home {
      box-shadow: -61px 0 $device-purple-dark, 61px 0 $device-purple-dark;
    }
  }
}
