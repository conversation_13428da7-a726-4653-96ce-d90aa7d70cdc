import { LoadMore } from "./LoadMore";
import { useState } from "preact/hooks";

export default {
  component: LoadMore,
};

// 演示无限滚动效果的交互式故事
export const InfiniteScrollDemo = {
  render: () => {
    const [loadCount, setLoadCount] = useState(0);
    const [items, setItems] = useState(["Item 1", "Item 2", "Item 3"]);
    const [loading, setLoading] = useState(false);
    const [finished, setFinished] = useState(false);

    const handleLoad = () => {
      setLoading(true);

      // 模拟异步加载
      setTimeout(() => {
        const newLoadCount = loadCount + 1;
        setLoadCount(newLoadCount);

        // 添加新的模拟数据
        const newItems = Array.from(
          { length: 3 },
          (_, i) => `Item ${items.length + i + 1}`
        );
        setItems((prev) => [...prev, ...newItems]);

        // 加载3次后设置为 finished
        if (newLoadCount >= 3) {
          setFinished(true);
        } else {
          setLoading(false);
        }
      }, 1500); // 1.5秒模拟加载时间
    };

    return (
      <div className="max-w-md mx-auto p-4">
        <h3 className="text-lg font-semibold mb-4">无限滚动演示</h3>
        <p className="text-sm text-gray-600 mb-4">
          滚动到底部触发加载，将加载3次后显示结束状态
        </p>

        {/* 模拟内容列表 */}
        <div className="space-y-2 mb-4">
          {items.map((item, index) => (
            <div
              key={index}
              className="p-3 bg-gray-100 text-gray-950 rounded border"
            >
              {item}
            </div>
          ))}
        </div>

        {/* 加载状态信息 */}
        <div className="text-sm text-gray-500 mb-4">
          已加载次数: {loadCount}/3
        </div>

        {/* LoadMore 组件 */}
        <LoadMore loading={loading} finished={finished} onLoad={handleLoad} />
      </div>
    );
  },
  parameters: {
    docs: {
      description: {
        story:
          "这个故事演示了无限滚动的完整流程。当 LoadMore 组件进入视口时会触发加载，加载3次后会显示结束状态。",
      },
    },
  },
};
