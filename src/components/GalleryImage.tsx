import clsx from "clsx";

interface Props {
  focus?: boolean;
  imageSrc: string;
  imageSize: {
    width: number;
    height: number;
  };
  className?: string;
  onClick?: () => void;
}

export function GalleryImage({
  imageSize,
  className,
  focus,
  imageSrc,
  onClick,
  ...rest
}: Props) {
  const expectedImageHeight = 180;
  const expectedImageWidth =
    (expectedImageHeight / imageSize.height) * imageSize.width;
  return (
    <div
      {...rest}
      style={{
        width: `${expectedImageWidth}px`,
      }}
      class={clsx("relative", className)}
      onClick={onClick}
    >
      {/* 选中状态的背景 */}
      <div
        className={clsx(
          "absolute pointer-events-none -left-2 -top-2 h-[calc(100%+1rem)] w-[calc(100%+1rem)] rounded-2xl",
          focus && "bg-gray-700 border border-blue-200"
        )}
      ></div>

      {/* 图片 */}
      <div
        style={{ height: `${expectedImageHeight}px` }}
        className="bg-neutral-800 flex items-center justify-center w-full rounded-2xl overflow-hidden"
      >
        <img
          class="size-full relative object-cover"
          src={imageSrc}
          loading="lazy"
          decoding="async"
          alt=""
        />
      </div>

      {/* 文字描述 */}
      <div class="relative mt-2 text-xs bg-zinc-500/20 text-zinc-300 rounded-md py-0.5 px-1 inline-block max-w-full overflow-hidden text-ellipsis text-nowrap">
        {imageSize.width}
        <span> x </span>
        {imageSize.height}
      </div>
    </div>
  );
}
