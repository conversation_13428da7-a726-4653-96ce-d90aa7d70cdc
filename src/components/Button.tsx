import clsx from "clsx";
import type { PropsWithChildren } from "preact/compat";

interface ButtonProps {
  loading?: boolean;
  text: string;
  onClick: () => void;
}
export function TextButton({ text, onClick, loading = false }: ButtonProps) {
  return (
    <button
      className={clsx(
        "bg-slate-600 cursor-pointer w-full rounded-full flex items-center justify-center py-2 px-4"
      )}
      onClick={onClick}
      disabled={loading}
    >
      <span>{text}</span>
      {loading && (
        <span className="ml-2">
          <Spin />
        </span>
      )}
    </button>
  );
}

function Spin() {
  return (
    <svg
      className="animate-spin"
      xmlns="http://www.w3.org/2000/svg"
      width={24}
      height={24}
      viewBox="0 0 24 24"
    >
      <path
        fill="none"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M21 12a9 9 0 1 1-6.219-8.56"
      ></path>
    </svg>
  );
}
