import { Default<PERSON>pi, type <PERSON><PERSON>eyGetRequest } from "./api-client";
import { Configuration } from "./api-client/runtime";
import { extension } from "mime-types";

const configuration = new Configuration();
export const publicApi = new DefaultApi(configuration);

export async function downloadImage(requestParameters: ImageKeyGetRequest) {
  const res = (await publicApi.imageKeyGetRaw(requestParameters)).raw;
  if (!res.ok) return;
  const a = document.createElement("a");
  a.href = URL.createObjectURL(await res.blob());
  const filename = `${btoa(requestParameters.key)
    .replace(/=+$/, "")
    .slice(0, 12)}.${
    extension(res.headers.get("content-type") || "image/jpeg") || "jpg"
  }`;
  a.download = filename;
  a.click();
}
