import type { APIRoute } from "astro";
import { fetchAllTopicWallpapers, restApi } from "../../../api-buildtime";
import { SizeEnum } from "../../../common";
import { processImageURL } from "../../../helper";
import pMap from "p-map";

export const GET: APIRoute = async ({ params }) => {
  const id = Number(params.id);
  const res = await restApi.restWallpapersWallpaperIdSimilarGet({
    wallpaperId: id,
    size: [SizeEnum.Middle],
    limit: 20,
    scoreThreshold: 0.8,
  });

  const data: Wallpaper[] = (
    await pMap(res.results, async (i) => {
      let imageUrl: string;
      try {
        imageUrl = (await processImageURL(
          i.wallpaper.images[SizeEnum.Middle]
        )) as string;
      } catch (e) {
        console.error(e);
        return undefined;
      }
      return {
        id: i.wallpaper.id,
        imageUrl,
        height: i.wallpaper.height,
        width: i.wallpaper.width,
        md5: i.wallpaper.contentMd5,
      };
    })
  ).filter((i) => i !== undefined);
  return new Response(JSON.stringify(data));
};

export const getStaticPaths = async () => {
  const paths = [];
  for await (const item of fetchAllTopicWallpapers()) {
    paths.push({
      params: {
        id: item.id.toString(),
      },
    });
  }
  return paths;
};
