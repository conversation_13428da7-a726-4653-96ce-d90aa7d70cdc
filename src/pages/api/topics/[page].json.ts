import type { APIRoute } from "astro";
import type { GetStaticPaths } from "astro";
import { fetchTopicList, restApi } from "../../../api-buildtime";
import { TOPICS_PRE_PAGE } from "../../../common";

export const GET: APIRoute = async ({ params, props }) => {
  const values = await fetchTopicList(Number(params.page));
  return new Response(JSON.stringify(values));
};

export const getStaticPaths = async function () {
  const total = (
    await restApi.restClientsClientIdClienttopicsGet({
      clientId: import.meta.env.CLIENT_ID,
      pageSize: 1,
    })
  ).total;
  const maxPage = Math.ceil(total / TOPICS_PRE_PAGE);

  return Array.from({ length: maxPage - 1 }, (_, i) => {
    return {
      params: {
        page: i + 2,
      },
    };
  });
} satisfies GetStaticPaths;
