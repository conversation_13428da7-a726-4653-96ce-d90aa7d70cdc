import { useRef, useState } from "preact/hooks";
import { GalleryImage } from "../components/GalleryImage";
import { Drawer, type DrawerHandle } from "../components/Drawer";
import { WallpaperDetail } from "./WallpaperDetail";
import type { FetchTopicListResult } from "../api-buildtime";
import { LoadMore } from "../components/LoadMore";

export interface GalleryProps {
  initialValue: Topic[];
  next?: number;
}

export function GalleryView(props: GalleryProps) {
  const [selectedWallpaper, setSelectedWallpaper] = useState<Wallpaper>();

  const [drawerIsOpen, setDrawerIsOpen] = useState(false);

  const [topics, setTopics] = useState(() => [...props.initialValue]);

  const drawerRef = useRef<DrawerHandle>(null);

  const [moreLoading, setMoreLoading] = useState(false);
  const [moreFinished, setMoreFinished] = useState(props.next === undefined);
  const nextRef = useRef(props.next);
  return (
    <div className="w-screen flex">
      <div className="min-w-0 grow space-y-6">
        {topics.map((item) => (
          <div>
            <h6 className={"font-bold text-2xl pl-6 md:pl-12"}>{item.title}</h6>
            <div class="overflow-x-scroll w-full no-scrollbar py-4 ">
              <div className="flex gap-6 px-6 md:px-12">
                {item.wallpapers.map((wallpaper) => (
                  <div
                    className="skrink-0 cursor-pointer"
                    onClick={() => {
                      setSelectedWallpaper(wallpaper);
                      setDrawerIsOpen(true);
                    }}
                  >
                    <GalleryImage
                      focus={selectedWallpaper === wallpaper}
                      imageSrc={wallpaper.imageUrl}
                      imageSize={{
                        width: wallpaper.width,
                        height: wallpaper.height,
                      }}
                    />
                  </div>
                ))}
              </div>
            </div>
          </div>
        ))}
        <div className={"mb-4 text-center"}>
          <LoadMore
            loading={moreLoading}
            finished={moreFinished}
            onLoad={() => {
              if (nextRef.current === undefined) return;

              setMoreLoading(true);
              fetch(`/api/topics/${nextRef.current satisfies number}.json`)
                .then(async (resp) => {
                  if (resp.ok) {
                    const data = (await resp.json()) as FetchTopicListResult;
                    if (data.next === undefined) {
                      setMoreFinished(true);
                    } else {
                      nextRef.current = data.next;
                    }
                    setTopics((topics) => [...topics, ...data.results]);
                  }
                })
                .finally(() => {
                  setMoreLoading(false);
                });
            }}
          />
        </div>
      </div>

      <div className="shrink-0">
        <Drawer
          ref={drawerRef}
          elbowFirst={false}
          isOpen={drawerIsOpen}
          onOpenChange={setDrawerIsOpen}
          onClosed={() => {
            setSelectedWallpaper(undefined);
          }}
        >
          {selectedWallpaper && (
            <WallpaperDetail
              wallpaper={selectedWallpaper}
              onClickClose={() => {
                setDrawerIsOpen(false);
              }}
              onDisplayedWallpaperChange={() => {
                drawerRef.current?.scrollToTop();
              }}
            />
          )}
        </Drawer>
      </div>
    </div>
  );
}
