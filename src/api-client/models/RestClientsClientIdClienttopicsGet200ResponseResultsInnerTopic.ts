/* tslint:disable */
/* eslint-disable */
/**
 * Wallpapers API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 0.1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface RestClientsClientIdClienttopicsGet200ResponseResultsInnerTopic
 */
export interface RestClientsClientIdClienttopicsGet200ResponseResultsInnerTopic {
    /**
     * 
     * @type {number}
     * @memberof RestClientsClientIdClienttopicsGet200ResponseResultsInnerTopic
     */
    id: number;
}

/**
 * Check if a given object implements the RestClientsClientIdClienttopicsGet200ResponseResultsInnerTopic interface.
 */
export function instanceOfRestClientsClientIdClienttopicsGet200ResponseResultsInnerTopic(value: object): value is RestClientsClientIdClienttopicsGet200ResponseResultsInnerTopic {
    if (!('id' in value) || value['id'] === undefined) return false;
    return true;
}

export function RestClientsClientIdClienttopicsGet200ResponseResultsInnerTopicFromJSON(json: any): RestClientsClientIdClienttopicsGet200ResponseResultsInnerTopic {
    return RestClientsClientIdClienttopicsGet200ResponseResultsInnerTopicFromJSONTyped(json, false);
}

export function RestClientsClientIdClienttopicsGet200ResponseResultsInnerTopicFromJSONTyped(json: any, ignoreDiscriminator: boolean): RestClientsClientIdClienttopicsGet200ResponseResultsInnerTopic {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'],
    };
}

export function RestClientsClientIdClienttopicsGet200ResponseResultsInnerTopicToJSON(json: any): RestClientsClientIdClienttopicsGet200ResponseResultsInnerTopic {
    return RestClientsClientIdClienttopicsGet200ResponseResultsInnerTopicToJSONTyped(json, false);
}

export function RestClientsClientIdClienttopicsGet200ResponseResultsInnerTopicToJSONTyped(value?: RestClientsClientIdClienttopicsGet200ResponseResultsInnerTopic | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'id': value['id'],
    };
}

