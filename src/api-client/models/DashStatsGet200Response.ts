/* tslint:disable */
/* eslint-disable */
/**
 * Wallpapers API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 0.1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface DashStatsGet200Response
 */
export interface DashStatsGet200Response {
    /**
     * 
     * @type {number}
     * @memberof DashStatsGet200Response
     */
    wallpaperCount: number;
    /**
     * 
     * @type {number}
     * @memberof DashStatsGet200Response
     */
    wallpaperInTopicCount: number;
    /**
     * 
     * @type {number}
     * @memberof DashStatsGet200Response
     */
    topicCount: number;
    /**
     * 
     * @type {number}
     * @memberof DashStatsGet200Response
     */
    topicInClientCount: number;
}

/**
 * Check if a given object implements the DashStatsGet200Response interface.
 */
export function instanceOfDashStatsGet200Response(value: object): value is DashStatsGet200Response {
    if (!('wallpaperCount' in value) || value['wallpaperCount'] === undefined) return false;
    if (!('wallpaperInTopicCount' in value) || value['wallpaperInTopicCount'] === undefined) return false;
    if (!('topicCount' in value) || value['topicCount'] === undefined) return false;
    if (!('topicInClientCount' in value) || value['topicInClientCount'] === undefined) return false;
    return true;
}

export function DashStatsGet200ResponseFromJSON(json: any): DashStatsGet200Response {
    return DashStatsGet200ResponseFromJSONTyped(json, false);
}

export function DashStatsGet200ResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): DashStatsGet200Response {
    if (json == null) {
        return json;
    }
    return {
        
        'wallpaperCount': json['wallpaper_count'],
        'wallpaperInTopicCount': json['wallpaper_in_topic_count'],
        'topicCount': json['topic_count'],
        'topicInClientCount': json['topic_in_client_count'],
    };
}

export function DashStatsGet200ResponseToJSON(json: any): DashStatsGet200Response {
    return DashStatsGet200ResponseToJSONTyped(json, false);
}

export function DashStatsGet200ResponseToJSONTyped(value?: DashStatsGet200Response | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'wallpaper_count': value['wallpaperCount'],
        'wallpaper_in_topic_count': value['wallpaperInTopicCount'],
        'topic_count': value['topicCount'],
        'topic_in_client_count': value['topicInClientCount'],
    };
}

