/* tslint:disable */
/* eslint-disable */
/**
 * Wallpapers API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 0.1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaperImages } from './RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaperImages';
import {
    RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaperImagesFromJSON,
    RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaperImagesFromJSONTyped,
    RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaperImagesToJSON,
    RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaperImagesToJSONTyped,
} from './RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaperImages';

/**
 * 
 * @export
 * @interface RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaper
 */
export interface RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaper {
    /**
     * 
     * @type {number}
     * @memberof RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaper
     */
    id: number;
    /**
     * 
     * @type {RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaperImages}
     * @memberof RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaper
     */
    images: RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaperImages;
    /**
     * 
     * @type {string}
     * @memberof RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaper
     */
    format: string;
    /**
     * 
     * @type {number}
     * @memberof RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaper
     */
    width: number;
    /**
     * 
     * @type {number}
     * @memberof RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaper
     */
    height: number;
    /**
     * 单位为字节
     * @type {number}
     * @memberof RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaper
     */
    filesize: number;
    /**
     * 
     * @type {string}
     * @memberof RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaper
     */
    contentMd5: string;
}

/**
 * Check if a given object implements the RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaper interface.
 */
export function instanceOfRestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaper(value: object): value is RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaper {
    if (!('id' in value) || value['id'] === undefined) return false;
    if (!('images' in value) || value['images'] === undefined) return false;
    if (!('format' in value) || value['format'] === undefined) return false;
    if (!('width' in value) || value['width'] === undefined) return false;
    if (!('height' in value) || value['height'] === undefined) return false;
    if (!('filesize' in value) || value['filesize'] === undefined) return false;
    if (!('contentMd5' in value) || value['contentMd5'] === undefined) return false;
    return true;
}

export function RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaperFromJSON(json: any): RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaper {
    return RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaperFromJSONTyped(json, false);
}

export function RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaperFromJSONTyped(json: any, ignoreDiscriminator: boolean): RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaper {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'],
        'images': RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaperImagesFromJSON(json['images']),
        'format': json['format'],
        'width': json['width'],
        'height': json['height'],
        'filesize': json['filesize'],
        'contentMd5': json['content_md5'],
    };
}

export function RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaperToJSON(json: any): RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaper {
    return RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaperToJSONTyped(json, false);
}

export function RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaperToJSONTyped(value?: RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaper | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'id': value['id'],
        'images': RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaperImagesToJSON(value['images']),
        'format': value['format'],
        'width': value['width'],
        'height': value['height'],
        'filesize': value['filesize'],
        'content_md5': value['contentMd5'],
    };
}

