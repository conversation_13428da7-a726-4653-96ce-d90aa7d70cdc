/* tslint:disable */
/* eslint-disable */
/**
 * Wallpapers API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 0.1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 图片列表，当未提供任何 size 时，将默认填充一个 default 字段
 * @export
 * @interface RestTopicsTopicIdWallpapersGet200ResponseResultsInnerImages
 */
export interface RestTopicsTopicIdWallpapersGet200ResponseResultsInnerImages {
    [key: string]: string | any;
    /**
     * 
     * @type {string}
     * @memberof RestTopicsTopicIdWallpapersGet200ResponseResultsInnerImages
     */
    _default?: string;
}

/**
 * Check if a given object implements the RestTopicsTopicIdWallpapersGet200ResponseResultsInnerImages interface.
 */
export function instanceOfRestTopicsTopicIdWallpapersGet200ResponseResultsInnerImages(value: object): value is RestTopicsTopicIdWallpapersGet200ResponseResultsInnerImages {
    return true;
}

export function RestTopicsTopicIdWallpapersGet200ResponseResultsInnerImagesFromJSON(json: any): RestTopicsTopicIdWallpapersGet200ResponseResultsInnerImages {
    return RestTopicsTopicIdWallpapersGet200ResponseResultsInnerImagesFromJSONTyped(json, false);
}

export function RestTopicsTopicIdWallpapersGet200ResponseResultsInnerImagesFromJSONTyped(json: any, ignoreDiscriminator: boolean): RestTopicsTopicIdWallpapersGet200ResponseResultsInnerImages {
    if (json == null) {
        return json;
    }
    return {
        
            ...json,
        '_default': json['default'] == null ? undefined : json['default'],
    };
}

export function RestTopicsTopicIdWallpapersGet200ResponseResultsInnerImagesToJSON(json: any): RestTopicsTopicIdWallpapersGet200ResponseResultsInnerImages {
    return RestTopicsTopicIdWallpapersGet200ResponseResultsInnerImagesToJSONTyped(json, false);
}

export function RestTopicsTopicIdWallpapersGet200ResponseResultsInnerImagesToJSONTyped(value?: RestTopicsTopicIdWallpapersGet200ResponseResultsInnerImages | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
            ...value,
        'default': value['_default'],
    };
}

