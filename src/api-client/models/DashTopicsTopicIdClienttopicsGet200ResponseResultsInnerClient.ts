/* tslint:disable */
/* eslint-disable */
/**
 * Wallpapers API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 0.1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface DashTopicsTopicIdClienttopicsGet200ResponseResultsInnerClient
 */
export interface DashTopicsTopicIdClienttopicsGet200ResponseResultsInnerClient {
    /**
     * 
     * @type {string}
     * @memberof DashTopicsTopicIdClienttopicsGet200ResponseResultsInnerClient
     */
    id?: string;
    /**
     * 
     * @type {string}
     * @memberof DashTopicsTopicIdClienttopicsGet200ResponseResultsInnerClient
     */
    name?: string;
}

/**
 * Check if a given object implements the DashTopicsTopicIdClienttopicsGet200ResponseResultsInnerClient interface.
 */
export function instanceOfDashTopicsTopicIdClienttopicsGet200ResponseResultsInnerClient(value: object): value is DashTopicsTopicIdClienttopicsGet200ResponseResultsInnerClient {
    return true;
}

export function DashTopicsTopicIdClienttopicsGet200ResponseResultsInnerClientFromJSON(json: any): DashTopicsTopicIdClienttopicsGet200ResponseResultsInnerClient {
    return DashTopicsTopicIdClienttopicsGet200ResponseResultsInnerClientFromJSONTyped(json, false);
}

export function DashTopicsTopicIdClienttopicsGet200ResponseResultsInnerClientFromJSONTyped(json: any, ignoreDiscriminator: boolean): DashTopicsTopicIdClienttopicsGet200ResponseResultsInnerClient {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'] == null ? undefined : json['id'],
        'name': json['name'] == null ? undefined : json['name'],
    };
}

export function DashTopicsTopicIdClienttopicsGet200ResponseResultsInnerClientToJSON(json: any): DashTopicsTopicIdClienttopicsGet200ResponseResultsInnerClient {
    return DashTopicsTopicIdClienttopicsGet200ResponseResultsInnerClientToJSONTyped(json, false);
}

export function DashTopicsTopicIdClienttopicsGet200ResponseResultsInnerClientToJSONTyped(value?: DashTopicsTopicIdClienttopicsGet200ResponseResultsInnerClient | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'id': value['id'],
        'name': value['name'],
    };
}

