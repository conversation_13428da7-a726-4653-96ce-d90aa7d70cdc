/* tslint:disable */
/* eslint-disable */
/**
 * Wallpapers API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 0.1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { DashTopicsTopicIdGet200Response } from './DashTopicsTopicIdGet200Response';
import {
    DashTopicsTopicIdGet200ResponseFromJSON,
    DashTopicsTopicIdGet200ResponseFromJSONTyped,
    DashTopicsTopicIdGet200ResponseToJSON,
    DashTopicsTopicIdGet200ResponseToJSONTyped,
} from './DashTopicsTopicIdGet200Response';
import type { DashTopicsTopicIdClienttopicsGet200ResponseResultsInnerClient } from './DashTopicsTopicIdClienttopicsGet200ResponseResultsInnerClient';
import {
    DashTopicsTopicIdClienttopicsGet200ResponseResultsInnerClientFromJSON,
    DashTopicsTopicIdClienttopicsGet200ResponseResultsInnerClientFromJSONTyped,
    DashTopicsTopicIdClienttopicsGet200ResponseResultsInnerClientToJSON,
    DashTopicsTopicIdClienttopicsGet200ResponseResultsInnerClientToJSONTyped,
} from './DashTopicsTopicIdClienttopicsGet200ResponseResultsInnerClient';

/**
 * 
 * @export
 * @interface DashTopicsTopicIdClienttopicsGet200ResponseResultsInner
 */
export interface DashTopicsTopicIdClienttopicsGet200ResponseResultsInner {
    /**
     * 
     * @type {number}
     * @memberof DashTopicsTopicIdClienttopicsGet200ResponseResultsInner
     */
    id?: number;
    /**
     * 
     * @type {string}
     * @memberof DashTopicsTopicIdClienttopicsGet200ResponseResultsInner
     */
    title?: string;
    /**
     * 
     * @type {DashTopicsTopicIdGet200Response}
     * @memberof DashTopicsTopicIdClienttopicsGet200ResponseResultsInner
     */
    topic?: DashTopicsTopicIdGet200Response;
    /**
     * 
     * @type {DashTopicsTopicIdClienttopicsGet200ResponseResultsInnerClient}
     * @memberof DashTopicsTopicIdClienttopicsGet200ResponseResultsInner
     */
    client?: DashTopicsTopicIdClienttopicsGet200ResponseResultsInnerClient;
    /**
     * 
     * @type {Date}
     * @memberof DashTopicsTopicIdClienttopicsGet200ResponseResultsInner
     */
    publishedAt?: Date;
}

/**
 * Check if a given object implements the DashTopicsTopicIdClienttopicsGet200ResponseResultsInner interface.
 */
export function instanceOfDashTopicsTopicIdClienttopicsGet200ResponseResultsInner(value: object): value is DashTopicsTopicIdClienttopicsGet200ResponseResultsInner {
    return true;
}

export function DashTopicsTopicIdClienttopicsGet200ResponseResultsInnerFromJSON(json: any): DashTopicsTopicIdClienttopicsGet200ResponseResultsInner {
    return DashTopicsTopicIdClienttopicsGet200ResponseResultsInnerFromJSONTyped(json, false);
}

export function DashTopicsTopicIdClienttopicsGet200ResponseResultsInnerFromJSONTyped(json: any, ignoreDiscriminator: boolean): DashTopicsTopicIdClienttopicsGet200ResponseResultsInner {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'] == null ? undefined : json['id'],
        'title': json['title'] == null ? undefined : json['title'],
        'topic': json['topic'] == null ? undefined : DashTopicsTopicIdGet200ResponseFromJSON(json['topic']),
        'client': json['client'] == null ? undefined : DashTopicsTopicIdClienttopicsGet200ResponseResultsInnerClientFromJSON(json['client']),
        'publishedAt': json['published_at'] == null ? undefined : (new Date(json['published_at'])),
    };
}

export function DashTopicsTopicIdClienttopicsGet200ResponseResultsInnerToJSON(json: any): DashTopicsTopicIdClienttopicsGet200ResponseResultsInner {
    return DashTopicsTopicIdClienttopicsGet200ResponseResultsInnerToJSONTyped(json, false);
}

export function DashTopicsTopicIdClienttopicsGet200ResponseResultsInnerToJSONTyped(value?: DashTopicsTopicIdClienttopicsGet200ResponseResultsInner | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'id': value['id'],
        'title': value['title'],
        'topic': DashTopicsTopicIdGet200ResponseToJSON(value['topic']),
        'client': DashTopicsTopicIdClienttopicsGet200ResponseResultsInnerClientToJSON(value['client']),
        'published_at': value['publishedAt'] == null ? undefined : ((value['publishedAt']).toISOString()),
    };
}

