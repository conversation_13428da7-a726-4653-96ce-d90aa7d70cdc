/* tslint:disable */
/* eslint-disable */
/**
 * Wallpapers API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 0.1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface DashTopicsTopicIdGet200Response
 */
export interface DashTopicsTopicIdGet200Response {
    /**
     * 
     * @type {number}
     * @memberof DashTopicsTopicIdGet200Response
     */
    id?: number;
    /**
     * 
     * @type {string}
     * @memberof DashTopicsTopicIdGet200Response
     */
    comment?: string;
}

/**
 * Check if a given object implements the DashTopicsTopicIdGet200Response interface.
 */
export function instanceOfDashTopicsTopicIdGet200Response(value: object): value is DashTopicsTopicIdGet200Response {
    return true;
}

export function DashTopicsTopicIdGet200ResponseFromJSON(json: any): DashTopicsTopicIdGet200Response {
    return DashTopicsTopicIdGet200ResponseFromJSONTyped(json, false);
}

export function DashTopicsTopicIdGet200ResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): DashTopicsTopicIdGet200Response {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'] == null ? undefined : json['id'],
        'comment': json['comment'] == null ? undefined : json['comment'],
    };
}

export function DashTopicsTopicIdGet200ResponseToJSON(json: any): DashTopicsTopicIdGet200Response {
    return DashTopicsTopicIdGet200ResponseToJSONTyped(json, false);
}

export function DashTopicsTopicIdGet200ResponseToJSONTyped(value?: DashTopicsTopicIdGet200Response | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'id': value['id'],
        'comment': value['comment'],
    };
}

