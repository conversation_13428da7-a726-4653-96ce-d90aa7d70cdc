/* tslint:disable */
/* eslint-disable */
/**
 * Wallpapers API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 0.1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface DashUploadwallpapersPostRequest
 */
export interface DashUploadwallpapersPostRequest {
    /**
     * 
     * @type {Array<string>}
     * @memberof DashUploadwallpapersPostRequest
     */
    urls: Array<string>;
}

/**
 * Check if a given object implements the DashUploadwallpapersPostRequest interface.
 */
export function instanceOfDashUploadwallpapersPostRequest(value: object): value is DashUploadwallpapersPostRequest {
    if (!('urls' in value) || value['urls'] === undefined) return false;
    return true;
}

export function DashUploadwallpapersPostRequestFromJSON(json: any): DashUploadwallpapersPostRequest {
    return DashUploadwallpapersPostRequestFromJSONTyped(json, false);
}

export function DashUploadwallpapersPostRequestFromJSONTyped(json: any, ignoreDiscriminator: boolean): DashUploadwallpapersPostRequest {
    if (json == null) {
        return json;
    }
    return {
        
        'urls': json['urls'],
    };
}

export function DashUploadwallpapersPostRequestToJSON(json: any): DashUploadwallpapersPostRequest {
    return DashUploadwallpapersPostRequestToJSONTyped(json, false);
}

export function DashUploadwallpapersPostRequestToJSONTyped(value?: DashUploadwallpapersPostRequest | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'urls': value['urls'],
    };
}

