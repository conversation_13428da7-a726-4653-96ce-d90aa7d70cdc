/* tslint:disable */
/* eslint-disable */
/**
 * Wallpapers API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 0.1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface WallpapersGetFiltersParameter
 */
export interface WallpapersGetFiltersParameter {
    /**
     * 
     * @type {string}
     * @memberof WallpapersGetFiltersParameter
     */
    sizedFor?: WallpapersGetFiltersParameterSizedForEnum;
    /**
     * 
     * @type {string}
     * @memberof WallpapersGetFiltersParameter
     */
    color?: WallpapersGetFiltersParameterColorEnum;
    /**
     * 
     * @type {string}
     * @memberof WallpapersGetFiltersParameter
     */
    tag?: string;
}


/**
 * @export
 */
export const WallpapersGetFiltersParameterSizedForEnum = {
    Desktop: 'desktop',
    Mobile: 'mobile'
} as const;
export type WallpapersGetFiltersParameterSizedForEnum = typeof WallpapersGetFiltersParameterSizedForEnum[keyof typeof WallpapersGetFiltersParameterSizedForEnum];

/**
 * @export
 */
export const WallpapersGetFiltersParameterColorEnum = {
    Red: 'red',
    Pink: 'pink',
    Orange: 'orange',
    Yellow: 'yellow',
    Green: 'green',
    Cyan: 'cyan',
    Blue: 'blue',
    Purple: 'purple',
    Black: 'black',
    White: 'white',
    Gray: 'gray'
} as const;
export type WallpapersGetFiltersParameterColorEnum = typeof WallpapersGetFiltersParameterColorEnum[keyof typeof WallpapersGetFiltersParameterColorEnum];


/**
 * Check if a given object implements the WallpapersGetFiltersParameter interface.
 */
export function instanceOfWallpapersGetFiltersParameter(value: object): value is WallpapersGetFiltersParameter {
    return true;
}

export function WallpapersGetFiltersParameterFromJSON(json: any): WallpapersGetFiltersParameter {
    return WallpapersGetFiltersParameterFromJSONTyped(json, false);
}

export function WallpapersGetFiltersParameterFromJSONTyped(json: any, ignoreDiscriminator: boolean): WallpapersGetFiltersParameter {
    if (json == null) {
        return json;
    }
    return {
        
        'sizedFor': json['sized_for'] == null ? undefined : json['sized_for'],
        'color': json['color'] == null ? undefined : json['color'],
        'tag': json['tag'] == null ? undefined : json['tag'],
    };
}

export function WallpapersGetFiltersParameterToJSON(json: any): WallpapersGetFiltersParameter {
    return WallpapersGetFiltersParameterToJSONTyped(json, false);
}

export function WallpapersGetFiltersParameterToJSONTyped(value?: WallpapersGetFiltersParameter | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'sized_for': value['sizedFor'],
        'color': value['color'],
        'tag': value['tag'],
    };
}

