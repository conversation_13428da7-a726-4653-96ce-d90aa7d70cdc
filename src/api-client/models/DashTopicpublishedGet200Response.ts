/* tslint:disable */
/* eslint-disable */
/**
 * Wallpapers API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 0.1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { DashTopicpublishedGet200ResponsePublishedInner } from './DashTopicpublishedGet200ResponsePublishedInner';
import {
    DashTopicpublishedGet200ResponsePublishedInnerFromJSON,
    DashTopicpublishedGet200ResponsePublishedInnerFromJSONTyped,
    DashTopicpublishedGet200ResponsePublishedInnerToJSON,
    DashTopicpublishedGet200ResponsePublishedInnerToJSONTyped,
} from './DashTopicpublishedGet200ResponsePublishedInner';

/**
 * 
 * @export
 * @interface DashTopicpublishedGet200Response
 */
export interface DashTopicpublishedGet200Response {
    /**
     * 
     * @type {Array<DashTopicpublishedGet200ResponsePublishedInner>}
     * @memberof DashTopicpublishedGet200Response
     */
    published: Array<DashTopicpublishedGet200ResponsePublishedInner>;
    /**
     * 
     * @type {number}
     * @memberof DashTopicpublishedGet200Response
     */
    currentPage: number;
    /**
     * 
     * @type {number}
     * @memberof DashTopicpublishedGet200Response
     */
    currentPageSize: number;
    /**
     * 
     * @type {number}
     * @memberof DashTopicpublishedGet200Response
     */
    total: number;
}

/**
 * Check if a given object implements the DashTopicpublishedGet200Response interface.
 */
export function instanceOfDashTopicpublishedGet200Response(value: object): value is DashTopicpublishedGet200Response {
    if (!('published' in value) || value['published'] === undefined) return false;
    if (!('currentPage' in value) || value['currentPage'] === undefined) return false;
    if (!('currentPageSize' in value) || value['currentPageSize'] === undefined) return false;
    if (!('total' in value) || value['total'] === undefined) return false;
    return true;
}

export function DashTopicpublishedGet200ResponseFromJSON(json: any): DashTopicpublishedGet200Response {
    return DashTopicpublishedGet200ResponseFromJSONTyped(json, false);
}

export function DashTopicpublishedGet200ResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): DashTopicpublishedGet200Response {
    if (json == null) {
        return json;
    }
    return {
        
        'published': ((json['published'] as Array<any>).map(DashTopicpublishedGet200ResponsePublishedInnerFromJSON)),
        'currentPage': json['current_page'],
        'currentPageSize': json['current_page_size'],
        'total': json['total'],
    };
}

export function DashTopicpublishedGet200ResponseToJSON(json: any): DashTopicpublishedGet200Response {
    return DashTopicpublishedGet200ResponseToJSONTyped(json, false);
}

export function DashTopicpublishedGet200ResponseToJSONTyped(value?: DashTopicpublishedGet200Response | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'published': ((value['published'] as Array<any>).map(DashTopicpublishedGet200ResponsePublishedInnerToJSON)),
        'current_page': value['currentPage'],
        'current_page_size': value['currentPageSize'],
        'total': value['total'],
    };
}

