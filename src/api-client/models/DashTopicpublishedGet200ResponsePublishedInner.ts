/* tslint:disable */
/* eslint-disable */
/**
 * Wallpapers API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 0.1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { DashTopicsPost200Response } from './DashTopicsPost200Response';
import {
    DashTopicsPost200ResponseFromJSON,
    DashTopicsPost200ResponseFromJSONTyped,
    DashTopicsPost200ResponseToJSON,
    DashTopicsPost200ResponseToJSONTyped,
} from './DashTopicsPost200Response';
import type { DashTopicpublishedGet200ResponsePublishedInnerClient } from './DashTopicpublishedGet200ResponsePublishedInnerClient';
import {
    DashTopicpublishedGet200ResponsePublishedInnerClientFromJSON,
    DashTopicpublishedGet200ResponsePublishedInnerClientFromJSONTyped,
    DashTopicpublishedGet200ResponsePublishedInnerClientToJSON,
    DashTopicpublishedGet200ResponsePublishedInnerClientToJSONTyped,
} from './DashTopicpublishedGet200ResponsePublishedInnerClient';

/**
 * 
 * @export
 * @interface DashTopicpublishedGet200ResponsePublishedInner
 */
export interface DashTopicpublishedGet200ResponsePublishedInner {
    /**
     * 
     * @type {number}
     * @memberof DashTopicpublishedGet200ResponsePublishedInner
     */
    id: number;
    /**
     * 
     * @type {string}
     * @memberof DashTopicpublishedGet200ResponsePublishedInner
     */
    title: string;
    /**
     * 
     * @type {DashTopicsPost200Response}
     * @memberof DashTopicpublishedGet200ResponsePublishedInner
     */
    topic: DashTopicsPost200Response;
    /**
     * 
     * @type {DashTopicpublishedGet200ResponsePublishedInnerClient}
     * @memberof DashTopicpublishedGet200ResponsePublishedInner
     */
    client: DashTopicpublishedGet200ResponsePublishedInnerClient;
    /**
     * 
     * @type {Date}
     * @memberof DashTopicpublishedGet200ResponsePublishedInner
     */
    publishedAt: Date;
}

/**
 * Check if a given object implements the DashTopicpublishedGet200ResponsePublishedInner interface.
 */
export function instanceOfDashTopicpublishedGet200ResponsePublishedInner(value: object): value is DashTopicpublishedGet200ResponsePublishedInner {
    if (!('id' in value) || value['id'] === undefined) return false;
    if (!('title' in value) || value['title'] === undefined) return false;
    if (!('topic' in value) || value['topic'] === undefined) return false;
    if (!('client' in value) || value['client'] === undefined) return false;
    if (!('publishedAt' in value) || value['publishedAt'] === undefined) return false;
    return true;
}

export function DashTopicpublishedGet200ResponsePublishedInnerFromJSON(json: any): DashTopicpublishedGet200ResponsePublishedInner {
    return DashTopicpublishedGet200ResponsePublishedInnerFromJSONTyped(json, false);
}

export function DashTopicpublishedGet200ResponsePublishedInnerFromJSONTyped(json: any, ignoreDiscriminator: boolean): DashTopicpublishedGet200ResponsePublishedInner {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'],
        'title': json['title'],
        'topic': DashTopicsPost200ResponseFromJSON(json['topic']),
        'client': DashTopicpublishedGet200ResponsePublishedInnerClientFromJSON(json['client']),
        'publishedAt': (new Date(json['published_at'])),
    };
}

export function DashTopicpublishedGet200ResponsePublishedInnerToJSON(json: any): DashTopicpublishedGet200ResponsePublishedInner {
    return DashTopicpublishedGet200ResponsePublishedInnerToJSONTyped(json, false);
}

export function DashTopicpublishedGet200ResponsePublishedInnerToJSONTyped(value?: DashTopicpublishedGet200ResponsePublishedInner | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'id': value['id'],
        'title': value['title'],
        'topic': DashTopicsPost200ResponseToJSON(value['topic']),
        'client': DashTopicpublishedGet200ResponsePublishedInnerClientToJSON(value['client']),
        'published_at': ((value['publishedAt']).toISOString()),
    };
}

