/* tslint:disable */
/* eslint-disable */
/**
 * Wallpapers API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 0.1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface DashTopicsPost200Response
 */
export interface DashTopicsPost200Response {
    /**
     * 
     * @type {number}
     * @memberof DashTopicsPost200Response
     */
    id: number;
    /**
     * 
     * @type {string}
     * @memberof DashTopicsPost200Response
     */
    comment: string;
}

/**
 * Check if a given object implements the DashTopicsPost200Response interface.
 */
export function instanceOfDashTopicsPost200Response(value: object): value is DashTopicsPost200Response {
    if (!('id' in value) || value['id'] === undefined) return false;
    if (!('comment' in value) || value['comment'] === undefined) return false;
    return true;
}

export function DashTopicsPost200ResponseFromJSON(json: any): DashTopicsPost200Response {
    return DashTopicsPost200ResponseFromJSONTyped(json, false);
}

export function DashTopicsPost200ResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): DashTopicsPost200Response {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'],
        'comment': json['comment'],
    };
}

export function DashTopicsPost200ResponseToJSON(json: any): DashTopicsPost200Response {
    return DashTopicsPost200ResponseToJSONTyped(json, false);
}

export function DashTopicsPost200ResponseToJSONTyped(value?: DashTopicsPost200Response | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'id': value['id'],
        'comment': value['comment'],
    };
}

