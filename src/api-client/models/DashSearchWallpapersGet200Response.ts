/* tslint:disable */
/* eslint-disable */
/**
 * Wallpapers API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 0.1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { DashSearchWallpapersGet200ResponseResultsInner } from './DashSearchWallpapersGet200ResponseResultsInner';
import {
    DashSearchWallpapersGet200ResponseResultsInnerFromJSON,
    DashSearchWallpapersGet200ResponseResultsInnerFromJSONTyped,
    DashSearchWallpapersGet200ResponseResultsInnerToJSON,
    DashSearchWallpapersGet200ResponseResultsInnerToJSONTyped,
} from './DashSearchWallpapersGet200ResponseResultsInner';

/**
 * 
 * @export
 * @interface DashSearchWallpapersGet200Response
 */
export interface DashSearchWallpapersGet200Response {
    /**
     * 
     * @type {string}
     * @memberof DashSearchWallpapersGet200Response
     */
    q: string;
    /**
     * 
     * @type {Array<DashSearchWallpapersGet200ResponseResultsInner>}
     * @memberof DashSearchWallpapersGet200Response
     */
    results: Array<DashSearchWallpapersGet200ResponseResultsInner>;
}

/**
 * Check if a given object implements the DashSearchWallpapersGet200Response interface.
 */
export function instanceOfDashSearchWallpapersGet200Response(value: object): value is DashSearchWallpapersGet200Response {
    if (!('q' in value) || value['q'] === undefined) return false;
    if (!('results' in value) || value['results'] === undefined) return false;
    return true;
}

export function DashSearchWallpapersGet200ResponseFromJSON(json: any): DashSearchWallpapersGet200Response {
    return DashSearchWallpapersGet200ResponseFromJSONTyped(json, false);
}

export function DashSearchWallpapersGet200ResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): DashSearchWallpapersGet200Response {
    if (json == null) {
        return json;
    }
    return {
        
        'q': json['q'],
        'results': ((json['results'] as Array<any>).map(DashSearchWallpapersGet200ResponseResultsInnerFromJSON)),
    };
}

export function DashSearchWallpapersGet200ResponseToJSON(json: any): DashSearchWallpapersGet200Response {
    return DashSearchWallpapersGet200ResponseToJSONTyped(json, false);
}

export function DashSearchWallpapersGet200ResponseToJSONTyped(value?: DashSearchWallpapersGet200Response | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'q': value['q'],
        'results': ((value['results'] as Array<any>).map(DashSearchWallpapersGet200ResponseResultsInnerToJSON)),
    };
}

