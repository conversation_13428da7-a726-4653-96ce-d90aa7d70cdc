/* tslint:disable */
/* eslint-disable */
/**
 * Wallpapers API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 0.1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { ClientsClientIdTopicsGet200ResponseClientTopicsInner } from './ClientsClientIdTopicsGet200ResponseClientTopicsInner';
import {
    ClientsClientIdTopicsGet200ResponseClientTopicsInnerFromJSON,
    ClientsClientIdTopicsGet200ResponseClientTopicsInnerFromJSONTyped,
    ClientsClientIdTopicsGet200ResponseClientTopicsInnerToJSON,
    ClientsClientIdTopicsGet200ResponseClientTopicsInnerToJSONTyped,
} from './ClientsClientIdTopicsGet200ResponseClientTopicsInner';

/**
 * 
 * @export
 * @interface ClientsClientIdTopicsGet200Response
 */
export interface ClientsClientIdTopicsGet200Response {
    /**
     * 
     * @type {Array<ClientsClientIdTopicsGet200ResponseClientTopicsInner>}
     * @memberof ClientsClientIdTopicsGet200Response
     */
    clientTopics: Array<ClientsClientIdTopicsGet200ResponseClientTopicsInner>;
}

/**
 * Check if a given object implements the ClientsClientIdTopicsGet200Response interface.
 */
export function instanceOfClientsClientIdTopicsGet200Response(value: object): value is ClientsClientIdTopicsGet200Response {
    if (!('clientTopics' in value) || value['clientTopics'] === undefined) return false;
    return true;
}

export function ClientsClientIdTopicsGet200ResponseFromJSON(json: any): ClientsClientIdTopicsGet200Response {
    return ClientsClientIdTopicsGet200ResponseFromJSONTyped(json, false);
}

export function ClientsClientIdTopicsGet200ResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): ClientsClientIdTopicsGet200Response {
    if (json == null) {
        return json;
    }
    return {
        
        'clientTopics': ((json['client_topics'] as Array<any>).map(ClientsClientIdTopicsGet200ResponseClientTopicsInnerFromJSON)),
    };
}

export function ClientsClientIdTopicsGet200ResponseToJSON(json: any): ClientsClientIdTopicsGet200Response {
    return ClientsClientIdTopicsGet200ResponseToJSONTyped(json, false);
}

export function ClientsClientIdTopicsGet200ResponseToJSONTyped(value?: ClientsClientIdTopicsGet200Response | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'client_topics': ((value['clientTopics'] as Array<any>).map(ClientsClientIdTopicsGet200ResponseClientTopicsInnerToJSON)),
    };
}

