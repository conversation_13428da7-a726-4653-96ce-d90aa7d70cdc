/* tslint:disable */
/* eslint-disable */
/**
 * Wallpapers API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 0.1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { WallpapersGet200ResponseWallpapersInner } from './WallpapersGet200ResponseWallpapersInner';
import {
    WallpapersGet200ResponseWallpapersInnerFromJSON,
    WallpapersGet200ResponseWallpapersInnerFromJSONTyped,
    WallpapersGet200ResponseWallpapersInnerToJSON,
    WallpapersGet200ResponseWallpapersInnerToJSONTyped,
} from './WallpapersGet200ResponseWallpapersInner';
import type { ClientsClientIdTopicsGet200ResponseClientTopicsInner } from './ClientsClientIdTopicsGet200ResponseClientTopicsInner';
import {
    ClientsClientIdTopicsGet200ResponseClientTopicsInnerFromJSON,
    ClientsClientIdTopicsGet200ResponseClientTopicsInnerFromJSONTyped,
    ClientsClientIdTopicsGet200ResponseClientTopicsInnerToJSON,
    ClientsClientIdTopicsGet200ResponseClientTopicsInnerToJSONTyped,
} from './ClientsClientIdTopicsGet200ResponseClientTopicsInner';

/**
 * 
 * @export
 * @interface ClientsClientIdTopicsTopicIdWallpapersGet200Response
 */
export interface ClientsClientIdTopicsTopicIdWallpapersGet200Response {
    /**
     * 
     * @type {ClientsClientIdTopicsGet200ResponseClientTopicsInner}
     * @memberof ClientsClientIdTopicsTopicIdWallpapersGet200Response
     */
    clientTopic: ClientsClientIdTopicsGet200ResponseClientTopicsInner;
    /**
     * 
     * @type {Array<WallpapersGet200ResponseWallpapersInner>}
     * @memberof ClientsClientIdTopicsTopicIdWallpapersGet200Response
     */
    wallpapers: Array<WallpapersGet200ResponseWallpapersInner>;
}

/**
 * Check if a given object implements the ClientsClientIdTopicsTopicIdWallpapersGet200Response interface.
 */
export function instanceOfClientsClientIdTopicsTopicIdWallpapersGet200Response(value: object): value is ClientsClientIdTopicsTopicIdWallpapersGet200Response {
    if (!('clientTopic' in value) || value['clientTopic'] === undefined) return false;
    if (!('wallpapers' in value) || value['wallpapers'] === undefined) return false;
    return true;
}

export function ClientsClientIdTopicsTopicIdWallpapersGet200ResponseFromJSON(json: any): ClientsClientIdTopicsTopicIdWallpapersGet200Response {
    return ClientsClientIdTopicsTopicIdWallpapersGet200ResponseFromJSONTyped(json, false);
}

export function ClientsClientIdTopicsTopicIdWallpapersGet200ResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): ClientsClientIdTopicsTopicIdWallpapersGet200Response {
    if (json == null) {
        return json;
    }
    return {
        
        'clientTopic': ClientsClientIdTopicsGet200ResponseClientTopicsInnerFromJSON(json['client_topic']),
        'wallpapers': ((json['wallpapers'] as Array<any>).map(WallpapersGet200ResponseWallpapersInnerFromJSON)),
    };
}

export function ClientsClientIdTopicsTopicIdWallpapersGet200ResponseToJSON(json: any): ClientsClientIdTopicsTopicIdWallpapersGet200Response {
    return ClientsClientIdTopicsTopicIdWallpapersGet200ResponseToJSONTyped(json, false);
}

export function ClientsClientIdTopicsTopicIdWallpapersGet200ResponseToJSONTyped(value?: ClientsClientIdTopicsTopicIdWallpapersGet200Response | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'client_topic': ClientsClientIdTopicsGet200ResponseClientTopicsInnerToJSON(value['clientTopic']),
        'wallpapers': ((value['wallpapers'] as Array<any>).map(WallpapersGet200ResponseWallpapersInnerToJSON)),
    };
}

