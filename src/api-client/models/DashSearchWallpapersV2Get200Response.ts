/* tslint:disable */
/* eslint-disable */
/**
 * Wallpapers API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 0.1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { DashSearchWallpapersV2Get200ResponseWallpapersInner } from './DashSearchWallpapersV2Get200ResponseWallpapersInner';
import {
    DashSearchWallpapersV2Get200ResponseWallpapersInnerFromJSON,
    DashSearchWallpapersV2Get200ResponseWallpapersInnerFromJSONTyped,
    DashSearchWallpapersV2Get200ResponseWallpapersInnerToJSON,
    DashSearchWallpapersV2Get200ResponseWallpapersInnerToJSONTyped,
} from './DashSearchWallpapersV2Get200ResponseWallpapersInner';

/**
 * 
 * @export
 * @interface DashSearchWallpapersV2Get200Response
 */
export interface DashSearchWallpapersV2Get200Response {
    /**
     * 
     * @type {string}
     * @memberof DashSearchWallpapersV2Get200Response
     */
    q: string;
    /**
     * 
     * @type {Array<DashSearchWallpapersV2Get200ResponseWallpapersInner>}
     * @memberof DashSearchWallpapersV2Get200Response
     */
    wallpapers: Array<DashSearchWallpapersV2Get200ResponseWallpapersInner>;
}

/**
 * Check if a given object implements the DashSearchWallpapersV2Get200Response interface.
 */
export function instanceOfDashSearchWallpapersV2Get200Response(value: object): value is DashSearchWallpapersV2Get200Response {
    if (!('q' in value) || value['q'] === undefined) return false;
    if (!('wallpapers' in value) || value['wallpapers'] === undefined) return false;
    return true;
}

export function DashSearchWallpapersV2Get200ResponseFromJSON(json: any): DashSearchWallpapersV2Get200Response {
    return DashSearchWallpapersV2Get200ResponseFromJSONTyped(json, false);
}

export function DashSearchWallpapersV2Get200ResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): DashSearchWallpapersV2Get200Response {
    if (json == null) {
        return json;
    }
    return {
        
        'q': json['q'],
        'wallpapers': ((json['wallpapers'] as Array<any>).map(DashSearchWallpapersV2Get200ResponseWallpapersInnerFromJSON)),
    };
}

export function DashSearchWallpapersV2Get200ResponseToJSON(json: any): DashSearchWallpapersV2Get200Response {
    return DashSearchWallpapersV2Get200ResponseToJSONTyped(json, false);
}

export function DashSearchWallpapersV2Get200ResponseToJSONTyped(value?: DashSearchWallpapersV2Get200Response | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'q': value['q'],
        'wallpapers': ((value['wallpapers'] as Array<any>).map(DashSearchWallpapersV2Get200ResponseWallpapersInnerToJSON)),
    };
}

