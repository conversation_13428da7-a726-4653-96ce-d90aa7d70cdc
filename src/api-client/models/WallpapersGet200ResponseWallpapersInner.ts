/* tslint:disable */
/* eslint-disable */
/**
 * Wallpapers API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 0.1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaperImages } from './RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaperImages';
import {
    RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaperImagesFromJSON,
    RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaperImagesFromJSONTyped,
    RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaperImagesToJSON,
    RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaperImagesToJSONTyped,
} from './RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaperImages';
import type { WallpapersGet200ResponseWallpapersInnerUploader } from './WallpapersGet200ResponseWallpapersInnerUploader';
import {
    WallpapersGet200ResponseWallpapersInnerUploaderFromJSON,
    WallpapersGet200ResponseWallpapersInnerUploaderFromJSONTyped,
    WallpapersGet200ResponseWallpapersInnerUploaderToJSON,
    WallpapersGet200ResponseWallpapersInnerUploaderToJSONTyped,
} from './WallpapersGet200ResponseWallpapersInnerUploader';

/**
 * 
 * @export
 * @interface WallpapersGet200ResponseWallpapersInner
 */
export interface WallpapersGet200ResponseWallpapersInner {
    /**
     * 
     * @type {RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaperImages}
     * @memberof WallpapersGet200ResponseWallpapersInner
     */
    images: RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaperImages;
    /**
     * 
     * @type {string}
     * @memberof WallpapersGet200ResponseWallpapersInner
     */
    format: string;
    /**
     * 
     * @type {number}
     * @memberof WallpapersGet200ResponseWallpapersInner
     */
    width: number;
    /**
     * 
     * @type {number}
     * @memberof WallpapersGet200ResponseWallpapersInner
     */
    height: number;
    /**
     * 单位为字节
     * @type {number}
     * @memberof WallpapersGet200ResponseWallpapersInner
     */
    filesize: number;
    /**
     * 
     * @type {string}
     * @memberof WallpapersGet200ResponseWallpapersInner
     */
    contentMd5: string;
    /**
     * 
     * @type {WallpapersGet200ResponseWallpapersInnerUploader}
     * @memberof WallpapersGet200ResponseWallpapersInner
     */
    uploader: WallpapersGet200ResponseWallpapersInnerUploader;
}

/**
 * Check if a given object implements the WallpapersGet200ResponseWallpapersInner interface.
 */
export function instanceOfWallpapersGet200ResponseWallpapersInner(value: object): value is WallpapersGet200ResponseWallpapersInner {
    if (!('images' in value) || value['images'] === undefined) return false;
    if (!('format' in value) || value['format'] === undefined) return false;
    if (!('width' in value) || value['width'] === undefined) return false;
    if (!('height' in value) || value['height'] === undefined) return false;
    if (!('filesize' in value) || value['filesize'] === undefined) return false;
    if (!('contentMd5' in value) || value['contentMd5'] === undefined) return false;
    if (!('uploader' in value) || value['uploader'] === undefined) return false;
    return true;
}

export function WallpapersGet200ResponseWallpapersInnerFromJSON(json: any): WallpapersGet200ResponseWallpapersInner {
    return WallpapersGet200ResponseWallpapersInnerFromJSONTyped(json, false);
}

export function WallpapersGet200ResponseWallpapersInnerFromJSONTyped(json: any, ignoreDiscriminator: boolean): WallpapersGet200ResponseWallpapersInner {
    if (json == null) {
        return json;
    }
    return {
        
        'images': RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaperImagesFromJSON(json['images']),
        'format': json['format'],
        'width': json['width'],
        'height': json['height'],
        'filesize': json['filesize'],
        'contentMd5': json['content_md5'],
        'uploader': WallpapersGet200ResponseWallpapersInnerUploaderFromJSON(json['uploader']),
    };
}

export function WallpapersGet200ResponseWallpapersInnerToJSON(json: any): WallpapersGet200ResponseWallpapersInner {
    return WallpapersGet200ResponseWallpapersInnerToJSONTyped(json, false);
}

export function WallpapersGet200ResponseWallpapersInnerToJSONTyped(value?: WallpapersGet200ResponseWallpapersInner | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'images': RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaperImagesToJSON(value['images']),
        'format': value['format'],
        'width': value['width'],
        'height': value['height'],
        'filesize': value['filesize'],
        'content_md5': value['contentMd5'],
        'uploader': WallpapersGet200ResponseWallpapersInnerUploaderToJSON(value['uploader']),
    };
}

