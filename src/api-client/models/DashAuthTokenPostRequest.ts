/* tslint:disable */
/* eslint-disable */
/**
 * Wallpapers API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 0.1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface DashAuthTokenPostRequest
 */
export interface DashAuthTokenPostRequest {
    /**
     * 
     * @type {string}
     * @memberof DashAuthTokenPostRequest
     */
    username: string;
    /**
     * 
     * @type {string}
     * @memberof DashAuthTokenPostRequest
     */
    password: string;
}

/**
 * Check if a given object implements the DashAuthTokenPostRequest interface.
 */
export function instanceOfDashAuthTokenPostRequest(value: object): value is DashAuthTokenPostRequest {
    if (!('username' in value) || value['username'] === undefined) return false;
    if (!('password' in value) || value['password'] === undefined) return false;
    return true;
}

export function DashAuthTokenPostRequestFromJSON(json: any): DashAuthTokenPostRequest {
    return DashAuthTokenPostRequestFromJSONTyped(json, false);
}

export function DashAuthTokenPostRequestFromJSONTyped(json: any, ignoreDiscriminator: boolean): DashAuthTokenPostRequest {
    if (json == null) {
        return json;
    }
    return {
        
        'username': json['username'],
        'password': json['password'],
    };
}

export function DashAuthTokenPostRequestToJSON(json: any): DashAuthTokenPostRequest {
    return DashAuthTokenPostRequestToJSONTyped(json, false);
}

export function DashAuthTokenPostRequestToJSONTyped(value?: DashAuthTokenPostRequest | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'username': value['username'],
        'password': value['password'],
    };
}

