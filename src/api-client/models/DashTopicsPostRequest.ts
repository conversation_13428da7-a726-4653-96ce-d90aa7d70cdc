/* tslint:disable */
/* eslint-disable */
/**
 * Wallpapers API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 0.1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface DashTopicsPostRequest
 */
export interface DashTopicsPostRequest {
    /**
     * 
     * @type {string}
     * @memberof DashTopicsPostRequest
     */
    comment: string;
}

/**
 * Check if a given object implements the DashTopicsPostRequest interface.
 */
export function instanceOfDashTopicsPostRequest(value: object): value is DashTopicsPostRequest {
    if (!('comment' in value) || value['comment'] === undefined) return false;
    return true;
}

export function DashTopicsPostRequestFromJSON(json: any): DashTopicsPostRequest {
    return DashTopicsPostRequestFromJSONTyped(json, false);
}

export function DashTopicsPostRequestFromJSONTyped(json: any, ignoreDiscriminator: boolean): DashTopicsPostRequest {
    if (json == null) {
        return json;
    }
    return {
        
        'comment': json['comment'],
    };
}

export function DashTopicsPostRequestToJSON(json: any): DashTopicsPostRequest {
    return DashTopicsPostRequestToJSONTyped(json, false);
}

export function DashTopicsPostRequestToJSONTyped(value?: DashTopicsPostRequest | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'comment': value['comment'],
    };
}

