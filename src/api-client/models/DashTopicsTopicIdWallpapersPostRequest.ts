/* tslint:disable */
/* eslint-disable */
/**
 * Wallpapers API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 0.1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface DashTopicsTopicIdWallpapersPostRequest
 */
export interface DashTopicsTopicIdWallpapersPostRequest {
    /**
     * 
     * @type {number}
     * @memberof DashTopicsTopicIdWallpapersPostRequest
     */
    wallpaperId: number;
}

/**
 * Check if a given object implements the DashTopicsTopicIdWallpapersPostRequest interface.
 */
export function instanceOfDashTopicsTopicIdWallpapersPostRequest(value: object): value is DashTopicsTopicIdWallpapersPostRequest {
    if (!('wallpaperId' in value) || value['wallpaperId'] === undefined) return false;
    return true;
}

export function DashTopicsTopicIdWallpapersPostRequestFromJSON(json: any): DashTopicsTopicIdWallpapersPostRequest {
    return DashTopicsTopicIdWallpapersPostRequestFromJSONTyped(json, false);
}

export function DashTopicsTopicIdWallpapersPostRequestFromJSONTyped(json: any, ignoreDiscriminator: boolean): DashTopicsTopicIdWallpapersPostRequest {
    if (json == null) {
        return json;
    }
    return {
        
        'wallpaperId': json['wallpaper_id'],
    };
}

export function DashTopicsTopicIdWallpapersPostRequestToJSON(json: any): DashTopicsTopicIdWallpapersPostRequest {
    return DashTopicsTopicIdWallpapersPostRequestToJSONTyped(json, false);
}

export function DashTopicsTopicIdWallpapersPostRequestToJSONTyped(value?: DashTopicsTopicIdWallpapersPostRequest | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'wallpaper_id': value['wallpaperId'],
    };
}

