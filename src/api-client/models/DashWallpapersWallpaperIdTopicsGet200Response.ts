/* tslint:disable */
/* eslint-disable */
/**
 * Wallpapers API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 0.1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { DashTopicsGet200ResponseResultsInner } from './DashTopicsGet200ResponseResultsInner';
import {
    DashTopicsGet200ResponseResultsInnerFromJSON,
    DashTopicsGet200ResponseResultsInnerFromJSONTyped,
    DashTopicsGet200ResponseResultsInnerToJSON,
    DashTopicsGet200ResponseResultsInnerToJSONTyped,
} from './DashTopicsGet200ResponseResultsInner';

/**
 * 
 * @export
 * @interface DashWallpapersWallpaperIdTopicsGet200Response
 */
export interface DashWallpapersWallpaperIdTopicsGet200Response {
    /**
     * 
     * @type {Array<DashTopicsGet200ResponseResultsInner>}
     * @memberof DashWallpapersWallpaperIdTopicsGet200Response
     */
    topics: Array<DashTopicsGet200ResponseResultsInner>;
}

/**
 * Check if a given object implements the DashWallpapersWallpaperIdTopicsGet200Response interface.
 */
export function instanceOfDashWallpapersWallpaperIdTopicsGet200Response(value: object): value is DashWallpapersWallpaperIdTopicsGet200Response {
    if (!('topics' in value) || value['topics'] === undefined) return false;
    return true;
}

export function DashWallpapersWallpaperIdTopicsGet200ResponseFromJSON(json: any): DashWallpapersWallpaperIdTopicsGet200Response {
    return DashWallpapersWallpaperIdTopicsGet200ResponseFromJSONTyped(json, false);
}

export function DashWallpapersWallpaperIdTopicsGet200ResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): DashWallpapersWallpaperIdTopicsGet200Response {
    if (json == null) {
        return json;
    }
    return {
        
        'topics': ((json['topics'] as Array<any>).map(DashTopicsGet200ResponseResultsInnerFromJSON)),
    };
}

export function DashWallpapersWallpaperIdTopicsGet200ResponseToJSON(json: any): DashWallpapersWallpaperIdTopicsGet200Response {
    return DashWallpapersWallpaperIdTopicsGet200ResponseToJSONTyped(json, false);
}

export function DashWallpapersWallpaperIdTopicsGet200ResponseToJSONTyped(value?: DashWallpapersWallpaperIdTopicsGet200Response | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'topics': ((value['topics'] as Array<any>).map(DashTopicsGet200ResponseResultsInnerToJSON)),
    };
}

