/* tslint:disable */
/* eslint-disable */
/**
 * Wallpapers API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 0.1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { RestWallpapersWallpaperIdSimilarGet200ResponseResultsInner } from './RestWallpapersWallpaperIdSimilarGet200ResponseResultsInner';
import {
    RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerFromJSON,
    RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerFromJSONTyped,
    RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerToJSON,
    RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerToJSONTyped,
} from './RestWallpapersWallpaperIdSimilarGet200ResponseResultsInner';

/**
 * 
 * @export
 * @interface RestWallpapersWallpaperIdSimilarGet200Response
 */
export interface RestWallpapersWallpaperIdSimilarGet200Response {
    /**
     * 
     * @type {Array<RestWallpapersWallpaperIdSimilarGet200ResponseResultsInner>}
     * @memberof RestWallpapersWallpaperIdSimilarGet200Response
     */
    results: Array<RestWallpapersWallpaperIdSimilarGet200ResponseResultsInner>;
}

/**
 * Check if a given object implements the RestWallpapersWallpaperIdSimilarGet200Response interface.
 */
export function instanceOfRestWallpapersWallpaperIdSimilarGet200Response(value: object): value is RestWallpapersWallpaperIdSimilarGet200Response {
    if (!('results' in value) || value['results'] === undefined) return false;
    return true;
}

export function RestWallpapersWallpaperIdSimilarGet200ResponseFromJSON(json: any): RestWallpapersWallpaperIdSimilarGet200Response {
    return RestWallpapersWallpaperIdSimilarGet200ResponseFromJSONTyped(json, false);
}

export function RestWallpapersWallpaperIdSimilarGet200ResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): RestWallpapersWallpaperIdSimilarGet200Response {
    if (json == null) {
        return json;
    }
    return {
        
        'results': ((json['results'] as Array<any>).map(RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerFromJSON)),
    };
}

export function RestWallpapersWallpaperIdSimilarGet200ResponseToJSON(json: any): RestWallpapersWallpaperIdSimilarGet200Response {
    return RestWallpapersWallpaperIdSimilarGet200ResponseToJSONTyped(json, false);
}

export function RestWallpapersWallpaperIdSimilarGet200ResponseToJSONTyped(value?: RestWallpapersWallpaperIdSimilarGet200Response | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'results': ((value['results'] as Array<any>).map(RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerToJSON)),
    };
}

