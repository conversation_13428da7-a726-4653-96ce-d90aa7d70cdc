/* tslint:disable */
/* eslint-disable */
/**
 * Wallpapers API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 0.1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { WallpapersGet200ResponseWallpapersInner } from './WallpapersGet200ResponseWallpapersInner';
import {
    WallpapersGet200ResponseWallpapersInnerFromJSON,
    WallpapersGet200ResponseWallpapersInnerFromJSONTyped,
    WallpapersGet200ResponseWallpapersInnerToJSON,
    WallpapersGet200ResponseWallpapersInnerToJSONTyped,
} from './WallpapersGet200ResponseWallpapersInner';
import type { TopicsGet200ResponseTopicsInner } from './TopicsGet200ResponseTopicsInner';
import {
    TopicsGet200ResponseTopicsInnerFromJSON,
    TopicsGet200ResponseTopicsInnerFromJSONTyped,
    TopicsGet200ResponseTopicsInnerToJSON,
    TopicsGet200ResponseTopicsInnerToJSONTyped,
} from './TopicsGet200ResponseTopicsInner';

/**
 * 
 * @export
 * @interface TopicsTopicIdWallpapersGet200Response
 */
export interface TopicsTopicIdWallpapersGet200Response {
    /**
     * 
     * @type {TopicsGet200ResponseTopicsInner}
     * @memberof TopicsTopicIdWallpapersGet200Response
     */
    topic: TopicsGet200ResponseTopicsInner;
    /**
     * 
     * @type {Array<WallpapersGet200ResponseWallpapersInner>}
     * @memberof TopicsTopicIdWallpapersGet200Response
     */
    wallpapers: Array<WallpapersGet200ResponseWallpapersInner>;
}

/**
 * Check if a given object implements the TopicsTopicIdWallpapersGet200Response interface.
 */
export function instanceOfTopicsTopicIdWallpapersGet200Response(value: object): value is TopicsTopicIdWallpapersGet200Response {
    if (!('topic' in value) || value['topic'] === undefined) return false;
    if (!('wallpapers' in value) || value['wallpapers'] === undefined) return false;
    return true;
}

export function TopicsTopicIdWallpapersGet200ResponseFromJSON(json: any): TopicsTopicIdWallpapersGet200Response {
    return TopicsTopicIdWallpapersGet200ResponseFromJSONTyped(json, false);
}

export function TopicsTopicIdWallpapersGet200ResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): TopicsTopicIdWallpapersGet200Response {
    if (json == null) {
        return json;
    }
    return {
        
        'topic': TopicsGet200ResponseTopicsInnerFromJSON(json['topic']),
        'wallpapers': ((json['wallpapers'] as Array<any>).map(WallpapersGet200ResponseWallpapersInnerFromJSON)),
    };
}

export function TopicsTopicIdWallpapersGet200ResponseToJSON(json: any): TopicsTopicIdWallpapersGet200Response {
    return TopicsTopicIdWallpapersGet200ResponseToJSONTyped(json, false);
}

export function TopicsTopicIdWallpapersGet200ResponseToJSONTyped(value?: TopicsTopicIdWallpapersGet200Response | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'topic': TopicsGet200ResponseTopicsInnerToJSON(value['topic']),
        'wallpapers': ((value['wallpapers'] as Array<any>).map(WallpapersGet200ResponseWallpapersInnerToJSON)),
    };
}

