/* tslint:disable */
/* eslint-disable */
/**
 * Wallpapers API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 0.1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface ClientsClientIdTopicsGet200ResponseClientTopicsInner
 */
export interface ClientsClientIdTopicsGet200ResponseClientTopicsInner {
    /**
     * 
     * @type {number}
     * @memberof ClientsClientIdTopicsGet200ResponseClientTopicsInner
     */
    id: number;
    /**
     * 
     * @type {string}
     * @memberof ClientsClientIdTopicsGet200ResponseClientTopicsInner
     */
    title: string;
    /**
     * 
     * @type {number}
     * @memberof ClientsClientIdTopicsGet200ResponseClientTopicsInner
     */
    topicId: number;
}

/**
 * Check if a given object implements the ClientsClientIdTopicsGet200ResponseClientTopicsInner interface.
 */
export function instanceOfClientsClientIdTopicsGet200ResponseClientTopicsInner(value: object): value is ClientsClientIdTopicsGet200ResponseClientTopicsInner {
    if (!('id' in value) || value['id'] === undefined) return false;
    if (!('title' in value) || value['title'] === undefined) return false;
    if (!('topicId' in value) || value['topicId'] === undefined) return false;
    return true;
}

export function ClientsClientIdTopicsGet200ResponseClientTopicsInnerFromJSON(json: any): ClientsClientIdTopicsGet200ResponseClientTopicsInner {
    return ClientsClientIdTopicsGet200ResponseClientTopicsInnerFromJSONTyped(json, false);
}

export function ClientsClientIdTopicsGet200ResponseClientTopicsInnerFromJSONTyped(json: any, ignoreDiscriminator: boolean): ClientsClientIdTopicsGet200ResponseClientTopicsInner {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'],
        'title': json['title'],
        'topicId': json['topic_id'],
    };
}

export function ClientsClientIdTopicsGet200ResponseClientTopicsInnerToJSON(json: any): ClientsClientIdTopicsGet200ResponseClientTopicsInner {
    return ClientsClientIdTopicsGet200ResponseClientTopicsInnerToJSONTyped(json, false);
}

export function ClientsClientIdTopicsGet200ResponseClientTopicsInnerToJSONTyped(value?: ClientsClientIdTopicsGet200ResponseClientTopicsInner | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'id': value['id'],
        'title': value['title'],
        'topic_id': value['topicId'],
    };
}

