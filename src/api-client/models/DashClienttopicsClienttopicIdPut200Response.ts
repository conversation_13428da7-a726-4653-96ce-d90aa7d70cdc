/* tslint:disable */
/* eslint-disable */
/**
 * Wallpapers API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 0.1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface DashClienttopicsClienttopicIdPut200Response
 */
export interface DashClienttopicsClienttopicIdPut200Response {
    /**
     * 
     * @type {number}
     * @memberof DashClienttopicsClienttopicIdPut200Response
     */
    id: number;
    /**
     * 
     * @type {string}
     * @memberof DashClienttopicsClienttopicIdPut200Response
     */
    title: string;
    /**
     * 
     * @type {Date}
     * @memberof DashClienttopicsClienttopicIdPut200Response
     */
    publishedAt: Date;
}

/**
 * Check if a given object implements the DashClienttopicsClienttopicIdPut200Response interface.
 */
export function instanceOfDashClienttopicsClienttopicIdPut200Response(value: object): value is DashClienttopicsClienttopicIdPut200Response {
    if (!('id' in value) || value['id'] === undefined) return false;
    if (!('title' in value) || value['title'] === undefined) return false;
    if (!('publishedAt' in value) || value['publishedAt'] === undefined) return false;
    return true;
}

export function DashClienttopicsClienttopicIdPut200ResponseFromJSON(json: any): DashClienttopicsClienttopicIdPut200Response {
    return DashClienttopicsClienttopicIdPut200ResponseFromJSONTyped(json, false);
}

export function DashClienttopicsClienttopicIdPut200ResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): DashClienttopicsClienttopicIdPut200Response {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'],
        'title': json['title'],
        'publishedAt': (new Date(json['published_at'])),
    };
}

export function DashClienttopicsClienttopicIdPut200ResponseToJSON(json: any): DashClienttopicsClienttopicIdPut200Response {
    return DashClienttopicsClienttopicIdPut200ResponseToJSONTyped(json, false);
}

export function DashClienttopicsClienttopicIdPut200ResponseToJSONTyped(value?: DashClienttopicsClienttopicIdPut200Response | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'id': value['id'],
        'title': value['title'],
        'published_at': ((value['publishedAt']).toISOString()),
    };
}

