/* tslint:disable */
/* eslint-disable */
/**
 * Wallpapers API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 0.1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { CategoriesColorsGet200ResponseColorsInner } from './CategoriesColorsGet200ResponseColorsInner';
import {
    CategoriesColorsGet200ResponseColorsInnerFromJSON,
    CategoriesColorsGet200ResponseColorsInnerFromJSONTyped,
    CategoriesColorsGet200ResponseColorsInnerToJSON,
    CategoriesColorsGet200ResponseColorsInnerToJSONTyped,
} from './CategoriesColorsGet200ResponseColorsInner';

/**
 * 
 * @export
 * @interface CategoriesColorsGet200Response
 */
export interface CategoriesColorsGet200Response {
    /**
     * 
     * @type {Array<CategoriesColorsGet200ResponseColorsInner>}
     * @memberof CategoriesColorsGet200Response
     */
    colors: Array<CategoriesColorsGet200ResponseColorsInner>;
}

/**
 * Check if a given object implements the CategoriesColorsGet200Response interface.
 */
export function instanceOfCategoriesColorsGet200Response(value: object): value is CategoriesColorsGet200Response {
    if (!('colors' in value) || value['colors'] === undefined) return false;
    return true;
}

export function CategoriesColorsGet200ResponseFromJSON(json: any): CategoriesColorsGet200Response {
    return CategoriesColorsGet200ResponseFromJSONTyped(json, false);
}

export function CategoriesColorsGet200ResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): CategoriesColorsGet200Response {
    if (json == null) {
        return json;
    }
    return {
        
        'colors': ((json['colors'] as Array<any>).map(CategoriesColorsGet200ResponseColorsInnerFromJSON)),
    };
}

export function CategoriesColorsGet200ResponseToJSON(json: any): CategoriesColorsGet200Response {
    return CategoriesColorsGet200ResponseToJSONTyped(json, false);
}

export function CategoriesColorsGet200ResponseToJSONTyped(value?: CategoriesColorsGet200Response | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'colors': ((value['colors'] as Array<any>).map(CategoriesColorsGet200ResponseColorsInnerToJSON)),
    };
}

