/* tslint:disable */
/* eslint-disable */
/**
 * Wallpapers API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 0.1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaperImages } from './RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaperImages';
import {
    RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaperImagesFromJSON,
    RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaperImagesFromJSONTyped,
    RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaperImagesToJSON,
    RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaperImagesToJSONTyped,
} from './RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaperImages';

/**
 * 
 * @export
 * @interface DashTopicsTopicIdWallpapersGet200ResponseResultsInner
 */
export interface DashTopicsTopicIdWallpapersGet200ResponseResultsInner {
    /**
     * 
     * @type {number}
     * @memberof DashTopicsTopicIdWallpapersGet200ResponseResultsInner
     */
    id: number;
    /**
     * 
     * @type {string}
     * @memberof DashTopicsTopicIdWallpapersGet200ResponseResultsInner
     */
    url: string;
    /**
     * 
     * @type {RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaperImages}
     * @memberof DashTopicsTopicIdWallpapersGet200ResponseResultsInner
     */
    images: RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaperImages;
    /**
     * 
     * @type {string}
     * @memberof DashTopicsTopicIdWallpapersGet200ResponseResultsInner
     */
    format: string;
    /**
     * 
     * @type {number}
     * @memberof DashTopicsTopicIdWallpapersGet200ResponseResultsInner
     */
    width: number;
    /**
     * 
     * @type {number}
     * @memberof DashTopicsTopicIdWallpapersGet200ResponseResultsInner
     */
    height: number;
    /**
     * 单位为字节
     * @type {number}
     * @memberof DashTopicsTopicIdWallpapersGet200ResponseResultsInner
     */
    filesize: number;
    /**
     * 
     * @type {string}
     * @memberof DashTopicsTopicIdWallpapersGet200ResponseResultsInner
     */
    contentMd5: string;
}

/**
 * Check if a given object implements the DashTopicsTopicIdWallpapersGet200ResponseResultsInner interface.
 */
export function instanceOfDashTopicsTopicIdWallpapersGet200ResponseResultsInner(value: object): value is DashTopicsTopicIdWallpapersGet200ResponseResultsInner {
    if (!('id' in value) || value['id'] === undefined) return false;
    if (!('url' in value) || value['url'] === undefined) return false;
    if (!('images' in value) || value['images'] === undefined) return false;
    if (!('format' in value) || value['format'] === undefined) return false;
    if (!('width' in value) || value['width'] === undefined) return false;
    if (!('height' in value) || value['height'] === undefined) return false;
    if (!('filesize' in value) || value['filesize'] === undefined) return false;
    if (!('contentMd5' in value) || value['contentMd5'] === undefined) return false;
    return true;
}

export function DashTopicsTopicIdWallpapersGet200ResponseResultsInnerFromJSON(json: any): DashTopicsTopicIdWallpapersGet200ResponseResultsInner {
    return DashTopicsTopicIdWallpapersGet200ResponseResultsInnerFromJSONTyped(json, false);
}

export function DashTopicsTopicIdWallpapersGet200ResponseResultsInnerFromJSONTyped(json: any, ignoreDiscriminator: boolean): DashTopicsTopicIdWallpapersGet200ResponseResultsInner {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'],
        'url': json['url'],
        'images': RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaperImagesFromJSON(json['images']),
        'format': json['format'],
        'width': json['width'],
        'height': json['height'],
        'filesize': json['filesize'],
        'contentMd5': json['content_md5'],
    };
}

export function DashTopicsTopicIdWallpapersGet200ResponseResultsInnerToJSON(json: any): DashTopicsTopicIdWallpapersGet200ResponseResultsInner {
    return DashTopicsTopicIdWallpapersGet200ResponseResultsInnerToJSONTyped(json, false);
}

export function DashTopicsTopicIdWallpapersGet200ResponseResultsInnerToJSONTyped(value?: DashTopicsTopicIdWallpapersGet200ResponseResultsInner | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'id': value['id'],
        'url': value['url'],
        'images': RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaperImagesToJSON(value['images']),
        'format': value['format'],
        'width': value['width'],
        'height': value['height'],
        'filesize': value['filesize'],
        'content_md5': value['contentMd5'],
    };
}

