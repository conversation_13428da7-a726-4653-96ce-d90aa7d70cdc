/* tslint:disable */
/* eslint-disable */
/**
 * Wallpapers API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 0.1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { DashTopicsTopicIdClienttopicsGet200ResponseResultsInner } from './DashTopicsTopicIdClienttopicsGet200ResponseResultsInner';
import {
    DashTopicsTopicIdClienttopicsGet200ResponseResultsInnerFromJSON,
    DashTopicsTopicIdClienttopicsGet200ResponseResultsInnerFromJSONTyped,
    DashTopicsTopicIdClienttopicsGet200ResponseResultsInnerToJSON,
    DashTopicsTopicIdClienttopicsGet200ResponseResultsInnerToJSONTyped,
} from './DashTopicsTopicIdClienttopicsGet200ResponseResultsInner';

/**
 * 
 * @export
 * @interface DashTopicsTopicIdClienttopicsGet200Response
 */
export interface DashTopicsTopicIdClienttopicsGet200Response {
    /**
     * 
     * @type {number}
     * @memberof DashTopicsTopicIdClienttopicsGet200Response
     */
    page?: number;
    /**
     * 
     * @type {number}
     * @memberof DashTopicsTopicIdClienttopicsGet200Response
     */
    pageSize?: number;
    /**
     * 
     * @type {number}
     * @memberof DashTopicsTopicIdClienttopicsGet200Response
     */
    total?: number;
    /**
     * 
     * @type {Array<DashTopicsTopicIdClienttopicsGet200ResponseResultsInner>}
     * @memberof DashTopicsTopicIdClienttopicsGet200Response
     */
    results?: Array<DashTopicsTopicIdClienttopicsGet200ResponseResultsInner>;
}

/**
 * Check if a given object implements the DashTopicsTopicIdClienttopicsGet200Response interface.
 */
export function instanceOfDashTopicsTopicIdClienttopicsGet200Response(value: object): value is DashTopicsTopicIdClienttopicsGet200Response {
    return true;
}

export function DashTopicsTopicIdClienttopicsGet200ResponseFromJSON(json: any): DashTopicsTopicIdClienttopicsGet200Response {
    return DashTopicsTopicIdClienttopicsGet200ResponseFromJSONTyped(json, false);
}

export function DashTopicsTopicIdClienttopicsGet200ResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): DashTopicsTopicIdClienttopicsGet200Response {
    if (json == null) {
        return json;
    }
    return {
        
        'page': json['page'] == null ? undefined : json['page'],
        'pageSize': json['page_size'] == null ? undefined : json['page_size'],
        'total': json['total'] == null ? undefined : json['total'],
        'results': json['results'] == null ? undefined : ((json['results'] as Array<any>).map(DashTopicsTopicIdClienttopicsGet200ResponseResultsInnerFromJSON)),
    };
}

export function DashTopicsTopicIdClienttopicsGet200ResponseToJSON(json: any): DashTopicsTopicIdClienttopicsGet200Response {
    return DashTopicsTopicIdClienttopicsGet200ResponseToJSONTyped(json, false);
}

export function DashTopicsTopicIdClienttopicsGet200ResponseToJSONTyped(value?: DashTopicsTopicIdClienttopicsGet200Response | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'page': value['page'],
        'page_size': value['pageSize'],
        'total': value['total'],
        'results': value['results'] == null ? undefined : ((value['results'] as Array<any>).map(DashTopicsTopicIdClienttopicsGet200ResponseResultsInnerToJSON)),
    };
}

