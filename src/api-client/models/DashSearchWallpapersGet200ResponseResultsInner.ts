/* tslint:disable */
/* eslint-disable */
/**
 * Wallpapers API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 0.1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaper } from './RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaper';
import {
    RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaperFromJSON,
    RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaperFromJSONTyped,
    RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaperToJSON,
    RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaperToJSONTyped,
} from './RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaper';

/**
 * 
 * @export
 * @interface DashSearchWallpapersGet200ResponseResultsInner
 */
export interface DashSearchWallpapersGet200ResponseResultsInner {
    /**
     * 
     * @type {number}
     * @memberof DashSearchWallpapersGet200ResponseResultsInner
     */
    id: number;
    /**
     * 
     * @type {RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaper}
     * @memberof DashSearchWallpapersGet200ResponseResultsInner
     */
    wallpaper: RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaper;
}

/**
 * Check if a given object implements the DashSearchWallpapersGet200ResponseResultsInner interface.
 */
export function instanceOfDashSearchWallpapersGet200ResponseResultsInner(value: object): value is DashSearchWallpapersGet200ResponseResultsInner {
    if (!('id' in value) || value['id'] === undefined) return false;
    if (!('wallpaper' in value) || value['wallpaper'] === undefined) return false;
    return true;
}

export function DashSearchWallpapersGet200ResponseResultsInnerFromJSON(json: any): DashSearchWallpapersGet200ResponseResultsInner {
    return DashSearchWallpapersGet200ResponseResultsInnerFromJSONTyped(json, false);
}

export function DashSearchWallpapersGet200ResponseResultsInnerFromJSONTyped(json: any, ignoreDiscriminator: boolean): DashSearchWallpapersGet200ResponseResultsInner {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'],
        'wallpaper': RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaperFromJSON(json['wallpaper']),
    };
}

export function DashSearchWallpapersGet200ResponseResultsInnerToJSON(json: any): DashSearchWallpapersGet200ResponseResultsInner {
    return DashSearchWallpapersGet200ResponseResultsInnerToJSONTyped(json, false);
}

export function DashSearchWallpapersGet200ResponseResultsInnerToJSONTyped(value?: DashSearchWallpapersGet200ResponseResultsInner | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'id': value['id'],
        'wallpaper': RestWallpapersWallpaperIdSimilarGet200ResponseResultsInnerWallpaperToJSON(value['wallpaper']),
    };
}

