/* tslint:disable */
/* eslint-disable */
/**
 * Wallpapers API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 0.1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { TopicsGet200ResponseTopicsInner } from './TopicsGet200ResponseTopicsInner';
import {
    TopicsGet200ResponseTopicsInnerFromJSON,
    TopicsGet200ResponseTopicsInnerFromJSONTyped,
    TopicsGet200ResponseTopicsInnerToJSON,
    TopicsGet200ResponseTopicsInnerToJSONTyped,
} from './TopicsGet200ResponseTopicsInner';

/**
 * 
 * @export
 * @interface TopicsGet200Response
 */
export interface TopicsGet200Response {
    /**
     * 
     * @type {Array<TopicsGet200ResponseTopicsInner>}
     * @memberof TopicsGet200Response
     */
    topics: Array<TopicsGet200ResponseTopicsInner>;
}

/**
 * Check if a given object implements the TopicsGet200Response interface.
 */
export function instanceOfTopicsGet200Response(value: object): value is TopicsGet200Response {
    if (!('topics' in value) || value['topics'] === undefined) return false;
    return true;
}

export function TopicsGet200ResponseFromJSON(json: any): TopicsGet200Response {
    return TopicsGet200ResponseFromJSONTyped(json, false);
}

export function TopicsGet200ResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): TopicsGet200Response {
    if (json == null) {
        return json;
    }
    return {
        
        'topics': ((json['topics'] as Array<any>).map(TopicsGet200ResponseTopicsInnerFromJSON)),
    };
}

export function TopicsGet200ResponseToJSON(json: any): TopicsGet200Response {
    return TopicsGet200ResponseToJSONTyped(json, false);
}

export function TopicsGet200ResponseToJSONTyped(value?: TopicsGet200Response | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'topics': ((value['topics'] as Array<any>).map(TopicsGet200ResponseTopicsInnerToJSON)),
    };
}

