/* tslint:disable */
/* eslint-disable */
/**
 * Wallpapers API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 0.1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { RestTopicsTopicIdWallpapersGet200ResponseResultsInnerImages } from './RestTopicsTopicIdWallpapersGet200ResponseResultsInnerImages';
import {
    RestTopicsTopicIdWallpapersGet200ResponseResultsInnerImagesFromJSON,
    RestTopicsTopicIdWallpapersGet200ResponseResultsInnerImagesFromJSONTyped,
    RestTopicsTopicIdWallpapersGet200ResponseResultsInnerImagesToJSON,
    RestTopicsTopicIdWallpapersGet200ResponseResultsInnerImagesToJSONTyped,
} from './RestTopicsTopicIdWallpapersGet200ResponseResultsInnerImages';

/**
 * 
 * @export
 * @interface DashSearchWallpapersGet200ResponseResultsInnerWallpaper
 */
export interface DashSearchWallpapersGet200ResponseResultsInnerWallpaper {
    /**
     * 
     * @type {number}
     * @memberof DashSearchWallpapersGet200ResponseResultsInnerWallpaper
     */
    id: number;
    /**
     * 
     * @type {RestTopicsTopicIdWallpapersGet200ResponseResultsInnerImages}
     * @memberof DashSearchWallpapersGet200ResponseResultsInnerWallpaper
     */
    images: RestTopicsTopicIdWallpapersGet200ResponseResultsInnerImages;
    /**
     * 
     * @type {string}
     * @memberof DashSearchWallpapersGet200ResponseResultsInnerWallpaper
     */
    format: string;
    /**
     * 
     * @type {number}
     * @memberof DashSearchWallpapersGet200ResponseResultsInnerWallpaper
     */
    width: number;
    /**
     * 
     * @type {number}
     * @memberof DashSearchWallpapersGet200ResponseResultsInnerWallpaper
     */
    height: number;
    /**
     * 单位为字节
     * @type {number}
     * @memberof DashSearchWallpapersGet200ResponseResultsInnerWallpaper
     */
    filesize: number;
    /**
     * 
     * @type {string}
     * @memberof DashSearchWallpapersGet200ResponseResultsInnerWallpaper
     */
    contentMd5: string;
}

/**
 * Check if a given object implements the DashSearchWallpapersGet200ResponseResultsInnerWallpaper interface.
 */
export function instanceOfDashSearchWallpapersGet200ResponseResultsInnerWallpaper(value: object): value is DashSearchWallpapersGet200ResponseResultsInnerWallpaper {
    if (!('id' in value) || value['id'] === undefined) return false;
    if (!('images' in value) || value['images'] === undefined) return false;
    if (!('format' in value) || value['format'] === undefined) return false;
    if (!('width' in value) || value['width'] === undefined) return false;
    if (!('height' in value) || value['height'] === undefined) return false;
    if (!('filesize' in value) || value['filesize'] === undefined) return false;
    if (!('contentMd5' in value) || value['contentMd5'] === undefined) return false;
    return true;
}

export function DashSearchWallpapersGet200ResponseResultsInnerWallpaperFromJSON(json: any): DashSearchWallpapersGet200ResponseResultsInnerWallpaper {
    return DashSearchWallpapersGet200ResponseResultsInnerWallpaperFromJSONTyped(json, false);
}

export function DashSearchWallpapersGet200ResponseResultsInnerWallpaperFromJSONTyped(json: any, ignoreDiscriminator: boolean): DashSearchWallpapersGet200ResponseResultsInnerWallpaper {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'],
        'images': RestTopicsTopicIdWallpapersGet200ResponseResultsInnerImagesFromJSON(json['images']),
        'format': json['format'],
        'width': json['width'],
        'height': json['height'],
        'filesize': json['filesize'],
        'contentMd5': json['content_md5'],
    };
}

export function DashSearchWallpapersGet200ResponseResultsInnerWallpaperToJSON(json: any): DashSearchWallpapersGet200ResponseResultsInnerWallpaper {
    return DashSearchWallpapersGet200ResponseResultsInnerWallpaperToJSONTyped(json, false);
}

export function DashSearchWallpapersGet200ResponseResultsInnerWallpaperToJSONTyped(value?: DashSearchWallpapersGet200ResponseResultsInnerWallpaper | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'id': value['id'],
        'images': RestTopicsTopicIdWallpapersGet200ResponseResultsInnerImagesToJSON(value['images']),
        'format': value['format'],
        'width': value['width'],
        'height': value['height'],
        'filesize': value['filesize'],
        'content_md5': value['contentMd5'],
    };
}

