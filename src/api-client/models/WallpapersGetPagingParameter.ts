/* tslint:disable */
/* eslint-disable */
/**
 * Wallpapers API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 0.1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface WallpapersGetPagingParameter
 */
export interface WallpapersGetPagingParameter {
    /**
     * 
     * @type {number}
     * @memberof WallpapersGetPagingParameter
     */
    page?: number;
    /**
     * 
     * @type {number}
     * @memberof WallpapersGetPagingParameter
     */
    pageSize?: number;
}

/**
 * Check if a given object implements the WallpapersGetPagingParameter interface.
 */
export function instanceOfWallpapersGetPagingParameter(value: object): value is WallpapersGetPagingParameter {
    return true;
}

export function WallpapersGetPagingParameterFromJSON(json: any): WallpapersGetPagingParameter {
    return WallpapersGetPagingParameterFromJSONTyped(json, false);
}

export function WallpapersGetPagingParameterFromJSONTyped(json: any, ignoreDiscriminator: boolean): WallpapersGetPagingParameter {
    if (json == null) {
        return json;
    }
    return {
        
        'page': json['page'] == null ? undefined : json['page'],
        'pageSize': json['page_size'] == null ? undefined : json['page_size'],
    };
}

export function WallpapersGetPagingParameterToJSON(json: any): WallpapersGetPagingParameter {
    return WallpapersGetPagingParameterToJSONTyped(json, false);
}

export function WallpapersGetPagingParameterToJSONTyped(value?: WallpapersGetPagingParameter | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'page': value['page'],
        'page_size': value['pageSize'],
    };
}

