/* tslint:disable */
/* eslint-disable */
/**
 * Wallpapers API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 0.1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { DashTopicsTopicIdWallpapersGet200ResponseResultsInner } from './DashTopicsTopicIdWallpapersGet200ResponseResultsInner';
import {
    DashTopicsTopicIdWallpapersGet200ResponseResultsInnerFromJSON,
    DashTopicsTopicIdWallpapersGet200ResponseResultsInnerFromJSONTyped,
    DashTopicsTopicIdWallpapersGet200ResponseResultsInnerToJSON,
    DashTopicsTopicIdWallpapersGet200ResponseResultsInnerToJSONTyped,
} from './DashTopicsTopicIdWallpapersGet200ResponseResultsInner';

/**
 * 
 * @export
 * @interface DashWallpapersWallpaperIdSimilarGet200ResponseResultsInner
 */
export interface DashWallpapersWallpaperIdSimilarGet200ResponseResultsInner {
    /**
     * 
     * @type {DashTopicsTopicIdWallpapersGet200ResponseResultsInner}
     * @memberof DashWallpapersWallpaperIdSimilarGet200ResponseResultsInner
     */
    wallpaper: DashTopicsTopicIdWallpapersGet200ResponseResultsInner;
    /**
     * 
     * @type {number}
     * @memberof DashWallpapersWallpaperIdSimilarGet200ResponseResultsInner
     */
    score: number;
}

/**
 * Check if a given object implements the DashWallpapersWallpaperIdSimilarGet200ResponseResultsInner interface.
 */
export function instanceOfDashWallpapersWallpaperIdSimilarGet200ResponseResultsInner(value: object): value is DashWallpapersWallpaperIdSimilarGet200ResponseResultsInner {
    if (!('wallpaper' in value) || value['wallpaper'] === undefined) return false;
    if (!('score' in value) || value['score'] === undefined) return false;
    return true;
}

export function DashWallpapersWallpaperIdSimilarGet200ResponseResultsInnerFromJSON(json: any): DashWallpapersWallpaperIdSimilarGet200ResponseResultsInner {
    return DashWallpapersWallpaperIdSimilarGet200ResponseResultsInnerFromJSONTyped(json, false);
}

export function DashWallpapersWallpaperIdSimilarGet200ResponseResultsInnerFromJSONTyped(json: any, ignoreDiscriminator: boolean): DashWallpapersWallpaperIdSimilarGet200ResponseResultsInner {
    if (json == null) {
        return json;
    }
    return {
        
        'wallpaper': DashTopicsTopicIdWallpapersGet200ResponseResultsInnerFromJSON(json['wallpaper']),
        'score': json['score'],
    };
}

export function DashWallpapersWallpaperIdSimilarGet200ResponseResultsInnerToJSON(json: any): DashWallpapersWallpaperIdSimilarGet200ResponseResultsInner {
    return DashWallpapersWallpaperIdSimilarGet200ResponseResultsInnerToJSONTyped(json, false);
}

export function DashWallpapersWallpaperIdSimilarGet200ResponseResultsInnerToJSONTyped(value?: DashWallpapersWallpaperIdSimilarGet200ResponseResultsInner | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'wallpaper': DashTopicsTopicIdWallpapersGet200ResponseResultsInnerToJSON(value['wallpaper']),
        'score': value['score'],
    };
}

