/* tslint:disable */
/* eslint-disable */
/**
 * Wallpapers API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 0.1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { RestTopicsTopicIdWallpapersGet200ResponseResultsInnerImages } from './RestTopicsTopicIdWallpapersGet200ResponseResultsInnerImages';
import {
    RestTopicsTopicIdWallpapersGet200ResponseResultsInnerImagesFromJSON,
    RestTopicsTopicIdWallpapersGet200ResponseResultsInnerImagesFromJSONTyped,
    RestTopicsTopicIdWallpapersGet200ResponseResultsInnerImagesToJSON,
    RestTopicsTopicIdWallpapersGet200ResponseResultsInnerImagesToJSONTyped,
} from './RestTopicsTopicIdWallpapersGet200ResponseResultsInnerImages';

/**
 * 
 * @export
 * @interface RestTopicsTopicIdWallpapersGet200ResponseResultsInner
 */
export interface RestTopicsTopicIdWallpapersGet200ResponseResultsInner {
    /**
     * 
     * @type {number}
     * @memberof RestTopicsTopicIdWallpapersGet200ResponseResultsInner
     */
    id: number;
    /**
     * 
     * @type {RestTopicsTopicIdWallpapersGet200ResponseResultsInnerImages}
     * @memberof RestTopicsTopicIdWallpapersGet200ResponseResultsInner
     */
    images: RestTopicsTopicIdWallpapersGet200ResponseResultsInnerImages;
    /**
     * 
     * @type {string}
     * @memberof RestTopicsTopicIdWallpapersGet200ResponseResultsInner
     */
    format: string;
    /**
     * 
     * @type {number}
     * @memberof RestTopicsTopicIdWallpapersGet200ResponseResultsInner
     */
    width: number;
    /**
     * 
     * @type {number}
     * @memberof RestTopicsTopicIdWallpapersGet200ResponseResultsInner
     */
    height: number;
    /**
     * 单位为字节
     * @type {number}
     * @memberof RestTopicsTopicIdWallpapersGet200ResponseResultsInner
     */
    filesize: number;
    /**
     * 
     * @type {string}
     * @memberof RestTopicsTopicIdWallpapersGet200ResponseResultsInner
     */
    contentMd5: string;
}

/**
 * Check if a given object implements the RestTopicsTopicIdWallpapersGet200ResponseResultsInner interface.
 */
export function instanceOfRestTopicsTopicIdWallpapersGet200ResponseResultsInner(value: object): value is RestTopicsTopicIdWallpapersGet200ResponseResultsInner {
    if (!('id' in value) || value['id'] === undefined) return false;
    if (!('images' in value) || value['images'] === undefined) return false;
    if (!('format' in value) || value['format'] === undefined) return false;
    if (!('width' in value) || value['width'] === undefined) return false;
    if (!('height' in value) || value['height'] === undefined) return false;
    if (!('filesize' in value) || value['filesize'] === undefined) return false;
    if (!('contentMd5' in value) || value['contentMd5'] === undefined) return false;
    return true;
}

export function RestTopicsTopicIdWallpapersGet200ResponseResultsInnerFromJSON(json: any): RestTopicsTopicIdWallpapersGet200ResponseResultsInner {
    return RestTopicsTopicIdWallpapersGet200ResponseResultsInnerFromJSONTyped(json, false);
}

export function RestTopicsTopicIdWallpapersGet200ResponseResultsInnerFromJSONTyped(json: any, ignoreDiscriminator: boolean): RestTopicsTopicIdWallpapersGet200ResponseResultsInner {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'],
        'images': RestTopicsTopicIdWallpapersGet200ResponseResultsInnerImagesFromJSON(json['images']),
        'format': json['format'],
        'width': json['width'],
        'height': json['height'],
        'filesize': json['filesize'],
        'contentMd5': json['content_md5'],
    };
}

export function RestTopicsTopicIdWallpapersGet200ResponseResultsInnerToJSON(json: any): RestTopicsTopicIdWallpapersGet200ResponseResultsInner {
    return RestTopicsTopicIdWallpapersGet200ResponseResultsInnerToJSONTyped(json, false);
}

export function RestTopicsTopicIdWallpapersGet200ResponseResultsInnerToJSONTyped(value?: RestTopicsTopicIdWallpapersGet200ResponseResultsInner | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'id': value['id'],
        'images': RestTopicsTopicIdWallpapersGet200ResponseResultsInnerImagesToJSON(value['images']),
        'format': value['format'],
        'width': value['width'],
        'height': value['height'],
        'filesize': value['filesize'],
        'content_md5': value['contentMd5'],
    };
}

