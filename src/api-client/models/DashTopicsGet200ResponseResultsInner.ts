/* tslint:disable */
/* eslint-disable */
/**
 * Wallpapers API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 0.1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface DashTopicsGet200ResponseResultsInner
 */
export interface DashTopicsGet200ResponseResultsInner {
    /**
     * 
     * @type {number}
     * @memberof DashTopicsGet200ResponseResultsInner
     */
    id: number;
    /**
     * 
     * @type {string}
     * @memberof DashTopicsGet200ResponseResultsInner
     */
    comment: string;
}

/**
 * Check if a given object implements the DashTopicsGet200ResponseResultsInner interface.
 */
export function instanceOfDashTopicsGet200ResponseResultsInner(value: object): value is DashTopicsGet200ResponseResultsInner {
    if (!('id' in value) || value['id'] === undefined) return false;
    if (!('comment' in value) || value['comment'] === undefined) return false;
    return true;
}

export function DashTopicsGet200ResponseResultsInnerFromJSON(json: any): DashTopicsGet200ResponseResultsInner {
    return DashTopicsGet200ResponseResultsInnerFromJSONTyped(json, false);
}

export function DashTopicsGet200ResponseResultsInnerFromJSONTyped(json: any, ignoreDiscriminator: boolean): DashTopicsGet200ResponseResultsInner {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'],
        'comment': json['comment'],
    };
}

export function DashTopicsGet200ResponseResultsInnerToJSON(json: any): DashTopicsGet200ResponseResultsInner {
    return DashTopicsGet200ResponseResultsInnerToJSONTyped(json, false);
}

export function DashTopicsGet200ResponseResultsInnerToJSONTyped(value?: DashTopicsGet200ResponseResultsInner | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'id': value['id'],
        'comment': value['comment'],
    };
}

