/* tslint:disable */
/* eslint-disable */
/**
 * Wallpapers API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 0.1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { RestClientsClientIdClienttopicsGet200ResponseResultsInner } from './RestClientsClientIdClienttopicsGet200ResponseResultsInner';
import {
    RestClientsClientIdClienttopicsGet200ResponseResultsInnerFromJSON,
    RestClientsClientIdClienttopicsGet200ResponseResultsInnerFromJSONTyped,
    RestClientsClientIdClienttopicsGet200ResponseResultsInnerToJSON,
    RestClientsClientIdClienttopicsGet200ResponseResultsInnerToJSONTyped,
} from './RestClientsClientIdClienttopicsGet200ResponseResultsInner';

/**
 * 
 * @export
 * @interface RestClientsClientIdClienttopicsGet200Response
 */
export interface RestClientsClientIdClienttopicsGet200Response {
    /**
     * 
     * @type {Array<RestClientsClientIdClienttopicsGet200ResponseResultsInner>}
     * @memberof RestClientsClientIdClienttopicsGet200Response
     */
    results: Array<RestClientsClientIdClienttopicsGet200ResponseResultsInner>;
    /**
     * 
     * @type {number}
     * @memberof RestClientsClientIdClienttopicsGet200Response
     */
    page: number;
    /**
     * 
     * @type {number}
     * @memberof RestClientsClientIdClienttopicsGet200Response
     */
    pageSize: number;
    /**
     * 
     * @type {number}
     * @memberof RestClientsClientIdClienttopicsGet200Response
     */
    total: number;
}

/**
 * Check if a given object implements the RestClientsClientIdClienttopicsGet200Response interface.
 */
export function instanceOfRestClientsClientIdClienttopicsGet200Response(value: object): value is RestClientsClientIdClienttopicsGet200Response {
    if (!('results' in value) || value['results'] === undefined) return false;
    if (!('page' in value) || value['page'] === undefined) return false;
    if (!('pageSize' in value) || value['pageSize'] === undefined) return false;
    if (!('total' in value) || value['total'] === undefined) return false;
    return true;
}

export function RestClientsClientIdClienttopicsGet200ResponseFromJSON(json: any): RestClientsClientIdClienttopicsGet200Response {
    return RestClientsClientIdClienttopicsGet200ResponseFromJSONTyped(json, false);
}

export function RestClientsClientIdClienttopicsGet200ResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): RestClientsClientIdClienttopicsGet200Response {
    if (json == null) {
        return json;
    }
    return {
        
        'results': ((json['results'] as Array<any>).map(RestClientsClientIdClienttopicsGet200ResponseResultsInnerFromJSON)),
        'page': json['page'],
        'pageSize': json['page_size'],
        'total': json['total'],
    };
}

export function RestClientsClientIdClienttopicsGet200ResponseToJSON(json: any): RestClientsClientIdClienttopicsGet200Response {
    return RestClientsClientIdClienttopicsGet200ResponseToJSONTyped(json, false);
}

export function RestClientsClientIdClienttopicsGet200ResponseToJSONTyped(value?: RestClientsClientIdClienttopicsGet200Response | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'results': ((value['results'] as Array<any>).map(RestClientsClientIdClienttopicsGet200ResponseResultsInnerToJSON)),
        'page': value['page'],
        'page_size': value['pageSize'],
        'total': value['total'],
    };
}

