// 类型声明文件，用于解决Preact和React组件库的兼容性问题
declare module "@radix-ui/react-dialog" {
  import { ComponentChildren } from "preact";
  
  export interface DialogRootProps {
    children?: ComponentChildren;
    defaultOpen?: boolean;
    open?: boolean;
    onOpenChange?: (open: boolean) => void;
    modal?: boolean;
  }
  
  export interface DialogPortalProps {
    children?: ComponentChildren;
    container?: Element;
    forceMount?: true;
  }
  
  export interface DialogContentProps {
    children?: ComponentChildren;
    className?: string;
    onOpenAutoFocus?: (event: Event) => void;
    onCloseAutoFocus?: (event: Event) => void;
    onEscapeKeyDown?: (event: KeyboardEvent) => void;
    onPointerDownOutside?: (event: PointerEvent) => void;
    onInteractOutside?: (event: Event) => void;
    forceMount?: true;
  }
  
  export interface DialogTitleProps {
    children?: ComponentChildren;
    className?: string;
  }
  
  export interface DialogDescriptionProps {
    children?: ComponentChildren;
    className?: string;
  }
  
  export interface DialogCloseProps {
    children?: ComponentChildren;
    className?: string;
    asChild?: boolean;
  }
  
  export const Root: (props: DialogRootProps) => any;
  export const Portal: (props: DialogPortalProps) => any;
  export const Content: (props: DialogContentProps) => any;
  export const Title: (props: DialogTitleProps) => any;
  export const Description: (props: DialogDescriptionProps) => any;
  export const Close: (props: DialogCloseProps) => any;
}
